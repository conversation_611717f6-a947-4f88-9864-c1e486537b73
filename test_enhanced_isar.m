% TEST_ENHANCED_ISAR - 测试增强型三维运动ISAR成像算法
%
% 该脚本用于测试和验证增强型三维运动ISAR成像算法的性能
% 包括算法正确性检查、性能对比和结果可视化

clear; close all; clc;

fprintf('========== 增强型三维运动ISAR成像算法测试 ==========\n');

% ==================== 1. 参数设置 ====================
fprintf('1. 设置测试参数...\n');

% 基本参数
test_params = struct();
test_params.enable_comparison = true; % 是否与原算法对比
test_params.enable_detailed_analysis = true; % 是否进行详细分析
test_params.save_results = true; % 是否保存结果

% 数据源选择
data_source = 'simulation'; % 'simulation' 或 'real_data'

% ==================== 2. 数据准备 ====================
fprintf('2. 准备测试数据...\n');

if strcmp(data_source, 'simulation')
    % 生成仿真数据
    [echo_data, sim_params] = generate_enhanced_simulation_data();
    fprintf('   使用增强型仿真数据\n');
else
    % 加载实际数据
    try
        load('shipx2.mat');
        load('s_r_tm2.mat');
        echo_data = s_r_tm2;

        % 设置基本参数
        sim_params = struct();
        sim_params.Num_r = size(echo_data, 1);
        sim_params.Num_tm = size(echo_data, 2);
        sim_params.PRF = 1400;
        sim_params.fc = 5.2e9;
        sim_params.c = 3e8;
        sim_params.B = 80e6;

        delta_r_res = sim_params.c / (2*sim_params.B);
        sim_params.r_axis = linspace(-sim_params.Num_r/2*delta_r_res, sim_params.Num_r/2*delta_r_res, sim_params.Num_r);
        sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);

        fprintf('   使用实际测量数据\n');
    catch
        fprintf('   实际数据加载失败，使用仿真数据\n');
        [echo_data, sim_params] = generate_enhanced_simulation_data();
        data_source = 'simulation';
    end
end

fprintf('   数据尺寸: %d x %d\n', size(echo_data, 1), size(echo_data, 2));

% ==================== 3. 算法参数配置 ====================
fprintf('3. 配置算法参数...\n');

params_proc = configure_enhanced_parameters();

% ==================== 4. 运行增强型算法 ====================
fprintf('4. 运行增强型三维运动ISAR成像算法...\n');

tic;
[ISAR_image_enhanced, motion_params, quality_metrics] = enhanced_3d_motion_isar(echo_data, params_proc, sim_params);
time_enhanced = toc;

fprintf('   增强型算法耗时: %.2f 秒\n', time_enhanced);

% ==================== 5. 对比测试（可选）====================
if test_params.enable_comparison
    fprintf('5. 运行原算法进行对比...\n');

    tic;
    [ISAR_image_original, ~, ~, ~, ~] = perform_isar_imaging_fused_admm(echo_data, params_proc, sim_params);
    time_original = toc;

    fprintf('   原算法耗时: %.2f 秒\n', time_original);
    fprintf('   算法加速比: %.2fx\n', time_original / time_enhanced);
end

% ==================== 6. 结果分析和可视化 ====================
fprintf('6. 分析和可视化结果...\n');

% 基本可视化
visualize_results(echo_data, ISAR_image_enhanced, ISAR_image_original, sim_params, params_proc);

% 详细分析
if test_params.enable_detailed_analysis
    perform_detailed_analysis(ISAR_image_enhanced, ISAR_image_original, echo_data, motion_params, quality_metrics);
end

% ==================== 7. 保存结果（可选）====================
if test_params.save_results
    fprintf('7. 保存测试结果...\n');
    save_test_results(ISAR_image_enhanced, ISAR_image_original, motion_params, quality_metrics, test_params);
end

fprintf('========== 测试完成 ==========\n');

% ==================== 子函数定义 ====================

function [echo_data, sim_params] = generate_enhanced_simulation_data()
% 生成增强型仿真数据，包含更复杂的三维运动

% 基本参数
sim_params.B = 80e6;
sim_params.c = 3e8;
sim_params.PRF = 1400;
sim_params.fc = 5.2e9;
sim_params.Num_r = 100;
sim_params.Num_tm = 700; % 增加方位单元数

% 时间和距离轴
delta_r_res = sim_params.c / (2*sim_params.B);
sim_params.r_axis = linspace(-50*delta_r_res, 50*delta_r_res, sim_params.Num_r);
sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);

% 增强型目标模型（更复杂的舰船结构）
target_points = generate_complex_ship_model();

% 复杂三维运动参数
motion_params.roll_rate = 0.08; % 翻滚角速度 (rad/s)
motion_params.pitch_rate = 0.05; % 俯仰角速度 (rad/s)
motion_params.yaw_rate = 0.15; % 偏航角速度 (rad/s)
motion_params.roll_accel = 0.02; % 翻滚角加速度 (rad/s^2)
motion_params.pitch_accel = 0.01; % 俯仰角加速度 (rad/s^2)
motion_params.yaw_accel = 0.03; % 偏航角加速度 (rad/s^2)

% 生成回波数据
echo_data = generate_complex_motion_echo(target_points, motion_params, sim_params);

fprintf('   生成了包含复杂三维运动的仿真数据\n');

end

function target_points = generate_complex_ship_model()
% 生成复杂的舰船散射点模型

% 舰船主体
main_body_x = linspace(-50, 50, 21);
main_body_y = zeros(size(main_body_x));
main_body_z = zeros(size(main_body_x));

% 舰桥结构
bridge_x = linspace(-10, 10, 5);
bridge_y = 5 * ones(size(bridge_x));
bridge_z = 10 * ones(size(bridge_x));

% 甲板结构
deck_x = [-40, -20, 0, 20, 40];
deck_y = [3, -3, 3, -3, 3];
deck_z = [2, 2, 2, 2, 2];

% 天线和设备
antenna_x = [0, 0, 0];
antenna_y = [0, 0, 0];
antenna_z = [15, 20, 25];

% 组合所有散射点
target_points = [main_body_x', main_body_y', main_body_z';
                 bridge_x', bridge_y', bridge_z';
                 deck_x', deck_y', deck_z';
                 antenna_x', antenna_y', antenna_z'];

% 缩放到合适的尺寸
target_points = target_points * 0.5;

end

function echo_data = generate_complex_motion_echo(target_points, motion_params, sim_params)
% 生成包含复杂三维运动的回波数据

num_points = size(target_points, 1);
echo_data = zeros(sim_params.Num_r, sim_params.Num_tm);

% 雷达视线方向
los_direction = [cos(pi/4)*cos(0), cos(pi/4)*sin(0), sin(pi/4)];

fprintf('   生成 %d 个散射点的回波...', num_points);

for p_idx = 1:num_points
    if mod(p_idx, 10) == 0
        fprintf('.');
    end

    % 当前散射点位置
    x0 = target_points(p_idx, 1);
    y0 = target_points(p_idx, 2);
    z0 = target_points(p_idx, 3);

    % 计算时变距离
    range_history = zeros(size(sim_params.tm));

    for t_idx = 1:length(sim_params.tm)
        t = sim_params.tm(t_idx);

        % 三维运动模型
        roll_angle = motion_params.roll_rate * t + 0.5 * motion_params.roll_accel * t^2;
        pitch_angle = motion_params.pitch_rate * t + 0.5 * motion_params.pitch_accel * t^2;
        yaw_angle = motion_params.yaw_rate * t + 0.5 * motion_params.yaw_accel * t^2;

        % 旋转矩阵
        Rx = [1, 0, 0; 0, cos(roll_angle), -sin(roll_angle); 0, sin(roll_angle), cos(roll_angle)];
        Ry = [cos(pitch_angle), 0, sin(pitch_angle); 0, 1, 0; -sin(pitch_angle), 0, cos(pitch_angle)];
        Rz = [cos(yaw_angle), -sin(yaw_angle), 0; sin(yaw_angle), cos(yaw_angle), 0; 0, 0, 1];

        % 总旋转矩阵
        R_total = Rz * Ry * Rx;

        % 旋转后的位置
        pos_rotated = R_total * [x0; y0; z0];

        % 计算距离
        range_history(t_idx) = dot(pos_rotated, los_direction');
    end

    % 生成回波信号
    phase_history = 4*pi*sim_params.fc/sim_params.c * range_history;

    % 散射强度（随机变化）
    amplitude = 1.0 + 0.3 * randn();
    if p_idx > 50 % 某些强散射点
        amplitude = amplitude * 1.5;
    end

    % 添加到回波数据
    for r_idx = 1:sim_params.Num_r
        range_delay = sim_params.r_axis(r_idx);
        sinc_response = sinc(2*sim_params.B/sim_params.c * (range_delay - range_history));
        echo_data(r_idx, :) = echo_data(r_idx, :) + amplitude * sinc_response .* exp(1j * phase_history);
    end
end

fprintf(' 完成\n');

% 添加噪声
noise_power = 0.01 * mean(abs(echo_data(:)).^2);
echo_data = echo_data + sqrt(noise_power/2) * (randn(size(echo_data)) + 1j*randn(size(echo_data)));

end

function params_proc = configure_enhanced_parameters()
% 配置增强型算法参数

params_proc = struct();

% 增强型VMD参数
params_proc.vmd.K = 4;
params_proc.vmd.alpha_vmd = 3000;
params_proc.vmd.tau_vmd = 0.1;
params_proc.vmd.tol_vmd_inner = 1e-4;
params_proc.vmd.max_iter_vmd_inner = 20;
params_proc.vmd.init_omega_method = 'adaptive';
params_proc.vmd.alpha_phase_guidance = 0.8;
params_proc.vmd.enable_frequency_tracking = true;
params_proc.vmd.bandwidth_penalty = 1.5;

% 增强型相位估计参数
params_proc.phase_est.poly_order = 4;
params_proc.phase_est.fd_search_range_factor = 0.8;
params_proc.phase_est.ka_search_pts = 51;
params_proc.phase_est.kb_search_pts = 51;
params_proc.phase_est.kc_search_pts = 31;
params_proc.phase_est.max_iter_phase_inner = 8;
params_proc.phase_est.enable_global_optimization = true;
params_proc.phase_est.motion_model = '3D_coupled';
params_proc.phase_est.adaptive_search = true;

% 增强型ADMM参数
params_proc.admm_global.rho_X = 2.0;
params_proc.admm_global.rho_U = 1.0;
params_proc.admm_global.rho_P = 1.0;
params_proc.admm_global.lambda_sparsity = 0.02;
params_proc.admm_global.max_iter = 15;
params_proc.admm_global.tol = 1e-4;
params_proc.admm_global.alpha_data_fidelity = 1.5;
params_proc.admm_global.alpha_phase_sharpness = 0.3;
params_proc.admm_global.enable_adaptive_rho = true;
params_proc.admm_global.convergence_check_interval = 3;

% 三维运动补偿参数
params_proc.motion_3d.enable_roll_compensation = true;
params_proc.motion_3d.enable_pitch_compensation = true;
params_proc.motion_3d.enable_yaw_compensation = true;
params_proc.motion_3d.coupling_factor = 0.5;
params_proc.motion_3d.motion_estimation_method = 'iterative_ml';
params_proc.motion_3d.reference_scatterer_selection = 'energy_based';

% 其他参数
params_proc.PRF = 1400;
params_proc.fc = 5.2e9;
params_proc.c = 3e8;

end

function visualize_results(echo_data, ISAR_enhanced, ISAR_original, sim_params, params_proc)
% 可视化结果对比

% 计算多普勒轴
doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, size(echo_data, 2));

% 原始数据FFT
raw_fft = fftshift(fft(echo_data, [], 2), 2);

% 移位处理
ISAR_enhanced_shifted = fftshift(ISAR_enhanced, 2);
ISAR_original_shifted = fftshift(ISAR_original, 2);

% 创建对比图
figure('Name', '增强型ISAR算法结果对比', 'Position', [100, 100, 1200, 800]);

% 原始数据
subplot(2,3,1);
imagesc(doppler_axis, sim_params.r_axis, 20*log10(abs(raw_fft)/max(abs(raw_fft(:)))));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (m)'); title('原始数据直接FFT');
colorbar; axis xy; caxis([-40, 0]); colormap(gca, 'jet');

% 原算法结果
subplot(2,3,2);
imagesc(doppler_axis, sim_params.r_axis, 20*log10(abs(ISAR_original_shifted)/max(abs(ISAR_original_shifted(:)))));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (m)'); title('原VMD-ADMM-DCFT算法');
colorbar; axis xy; caxis([-40, 0]); colormap(gca, 'jet');

% 增强型算法结果
subplot(2,3,3);
imagesc(doppler_axis, sim_params.r_axis, 20*log10(abs(ISAR_enhanced_shifted)/max(abs(ISAR_enhanced_shifted(:)))));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (m)'); title('增强型三维运动补偿算法');
colorbar; axis xy; caxis([-40, 0]); colormap(gca, 'jet');

% 改善效果
subplot(2,3,4);
improvement = abs(ISAR_enhanced_shifted) ./ (abs(ISAR_original_shifted) + eps);
imagesc(doppler_axis, sim_params.r_axis, improvement);
xlabel('多普勒频率 (Hz)'); ylabel('距离 (m)'); title('改善比率');
colorbar; axis xy; caxis([0.5, 2]); colormap(gca, 'hot');

% 距离剖面对比
subplot(2,3,5);
[~, center_range_idx] = min(abs(sim_params.r_axis));
range_profile_raw = abs(raw_fft(center_range_idx, :));
range_profile_original = abs(ISAR_original_shifted(center_range_idx, :));
range_profile_enhanced = abs(ISAR_enhanced_shifted(center_range_idx, :));

plot(doppler_axis, 20*log10(range_profile_raw/max(range_profile_raw)), 'b-', 'LineWidth', 1);
hold on;
plot(doppler_axis, 20*log10(range_profile_original/max(range_profile_original)), 'r--', 'LineWidth', 1.5);
plot(doppler_axis, 20*log10(range_profile_enhanced/max(range_profile_enhanced)), 'g-', 'LineWidth', 2);
xlabel('多普勒频率 (Hz)'); ylabel('幅度 (dB)'); title('中心距离单元剖面对比');
legend('原始FFT', '原算法', '增强算法', 'Location', 'best');
grid on; axis tight;

% 方位剖面对比
subplot(2,3,6);
[~, center_doppler_idx] = min(abs(doppler_axis));
azimuth_profile_raw = abs(raw_fft(:, center_doppler_idx));
azimuth_profile_original = abs(ISAR_original_shifted(:, center_doppler_idx));
azimuth_profile_enhanced = abs(ISAR_enhanced_shifted(:, center_doppler_idx));

plot(sim_params.r_axis, 20*log10(azimuth_profile_raw/max(azimuth_profile_raw)), 'b-', 'LineWidth', 1);
hold on;
plot(sim_params.r_axis, 20*log10(azimuth_profile_original/max(azimuth_profile_original)), 'r--', 'LineWidth', 1.5);
plot(sim_params.r_axis, 20*log10(azimuth_profile_enhanced/max(azimuth_profile_enhanced)), 'g-', 'LineWidth', 2);
xlabel('距离 (m)'); ylabel('幅度 (dB)'); title('中心多普勒单元剖面对比');
legend('原始FFT', '原算法', '增强算法', 'Location', 'best');
grid on; axis tight;

end

function perform_detailed_analysis(ISAR_enhanced, ISAR_original, echo_data, motion_params, quality_metrics)
% 执行详细分析

fprintf('\n==================== 详细性能分析 ====================\n');

% 计算质量指标
raw_fft = fft(echo_data, [], 2);

% 对比度分析
contrast_raw = calculate_image_contrast(abs(raw_fft));
contrast_original = calculate_image_contrast(abs(ISAR_original));
contrast_enhanced = calculate_image_contrast(abs(ISAR_enhanced));

fprintf('对比度分析:\n');
fprintf('  原始数据: %.4f\n', contrast_raw);
fprintf('  原算法: %.4f (提升 %.2fx)\n', contrast_original, contrast_original/contrast_raw);
fprintf('  增强算法: %.4f (提升 %.2fx)\n', contrast_enhanced, contrast_enhanced/contrast_raw);
fprintf('  相对改善: %.2fx\n', contrast_enhanced/contrast_original);

% 聚焦度分析
focus_raw = calculate_focus_measure(raw_fft);
focus_original = calculate_focus_measure(ISAR_original);
focus_enhanced = calculate_focus_measure(ISAR_enhanced);

fprintf('\n聚焦度分析:\n');
fprintf('  原始数据: %.4f\n', focus_raw);
fprintf('  原算法: %.4f (提升 %.2fx)\n', focus_original, focus_original/focus_raw);
fprintf('  增强算法: %.4f (提升 %.2fx)\n', focus_enhanced, focus_enhanced/focus_raw);
fprintf('  相对改善: %.2fx\n', focus_enhanced/focus_original);

% 运动参数分析
fprintf('\n估计的运动参数:\n');
fprintf('  翻滚角速度: %.4f rad/s (%.2f°/s)\n', motion_params.roll_rate, motion_params.roll_rate*180/pi);
fprintf('  俯仰角速度: %.4f rad/s (%.2f°/s)\n', motion_params.pitch_rate, motion_params.pitch_rate*180/pi);
fprintf('  偏航角速度: %.4f rad/s (%.2f°/s)\n', motion_params.yaw_rate, motion_params.yaw_rate*180/pi);

% 频谱分析
fprintf('\n频谱特性分析:\n');
spectrum_raw = abs(fft2(echo_data));
spectrum_enhanced = abs(fft2(ISAR_enhanced));

energy_concentration_raw = sum(spectrum_raw(:).^2) / sum(spectrum_raw(:))^2;
energy_concentration_enhanced = sum(spectrum_enhanced(:).^2) / sum(spectrum_enhanced(:))^2;

fprintf('  能量集中度 - 原始: %.4f\n', energy_concentration_raw);
fprintf('  能量集中度 - 增强: %.4f\n', energy_concentration_enhanced);
fprintf('  集中度提升: %.2fx\n', energy_concentration_enhanced/energy_concentration_raw);

end

function save_test_results(ISAR_enhanced, ISAR_original, motion_params, quality_metrics, test_params)
% 保存测试结果

timestamp = datestr(now, 'yyyymmdd_HHMMSS');
filename = sprintf('enhanced_isar_test_results_%s.mat', timestamp);

save(filename, 'ISAR_enhanced', 'ISAR_original', 'motion_params', 'quality_metrics', 'test_params');

fprintf('   结果已保存到: %s\n', filename);

% 生成报告
report_filename = sprintf('enhanced_isar_test_report_%s.txt', timestamp);
generate_test_report(report_filename, motion_params, quality_metrics);

end

function generate_test_report(filename, motion_params, quality_metrics)
% 生成测试报告

fid = fopen(filename, 'w');

fprintf(fid, '增强型三维运动ISAR成像算法测试报告\n');
fprintf(fid, '生成时间: %s\n\n', datestr(now));

fprintf(fid, '估计的运动参数:\n');
fprintf(fid, '  翻滚角速度: %.4f rad/s\n', motion_params.roll_rate);
fprintf(fid, '  俯仰角速度: %.4f rad/s\n', motion_params.pitch_rate);
fprintf(fid, '  偏航角速度: %.4f rad/s\n', motion_params.yaw_rate);

fprintf(fid, '\n算法性能评估:\n');
fprintf(fid, '  综合质量评分: %.4f\n', quality_metrics.overall_score);

fclose(fid);

fprintf('   测试报告已保存到: %s\n', filename);

end