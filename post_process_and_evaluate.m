function [ISAR_image_processed, quality_metrics] = post_process_and_evaluate(ISAR_image_raw, original_data, params_proc)
% POST_PROCESS_AND_EVALUATE - 后处理和质量评估
%
% 该函数对ISAR成像结果进行后处理，包括旁瓣抑制、噪声滤波和质量评估
%
% 输入:
%   ISAR_image_raw - 原始ISAR图像
%   original_data - 原始雷达数据
%   params_proc - 处理参数
%
% 输出:
%   ISAR_image_processed - 后处理的ISAR图像
%   quality_metrics - 质量指标

fprintf('开始后处理和质量评估...\n');

% ==================== 第一步：旁瓣抑制 ====================
fprintf('  应用旁瓣抑制...\n');
ISAR_image_sidelobe_suppressed = apply_sidelobe_suppression(ISAR_image_raw, params_proc);

% ==================== 第二步：噪声滤波 ====================
fprintf('  应用噪声滤波...\n');
ISAR_image_filtered = apply_noise_filtering(ISAR_image_sidelobe_suppressed, params_proc);

% ==================== 第三步：对比度增强 ====================
fprintf('  应用对比度增强...\n');
ISAR_image_enhanced = apply_contrast_enhancement(ISAR_image_filtered, params_proc);

% ==================== 第四步：质量评估 ====================
fprintf('  计算质量指标...\n');
quality_metrics = calculate_comprehensive_quality_metrics(ISAR_image_enhanced, ISAR_image_raw, original_data);

ISAR_image_processed = ISAR_image_enhanced;

fprintf('后处理和质量评估完成。\n');

end

% ==================== 旁瓣抑制函数 ====================
function ISAR_image_suppressed = apply_sidelobe_suppression(ISAR_image, params_proc)
% 应用多种旁瓣抑制技术

[num_range, num_azimuth] = size(ISAR_image);

% 方法1：自适应窗函数
window_range = get_adaptive_window(num_range, 'range');
window_azimuth = get_adaptive_window(num_azimuth, 'azimuth');

% 应用二维窗函数
window_2d = window_range(:) * window_azimuth(:)';
ISAR_image_windowed = ISAR_image .* window_2d;

% 方法2：基于能量的自适应阈值
energy_threshold = calculate_adaptive_threshold(ISAR_image);
ISAR_image_thresholded = apply_energy_threshold(ISAR_image_windowed, energy_threshold);

% 方法3：形态学滤波
ISAR_image_morphological = apply_morphological_filtering(ISAR_image_thresholded);

ISAR_image_suppressed = ISAR_image_morphological;

end

% ==================== 自适应窗函数 ====================
function window = get_adaptive_window(N, type)
% 获取自适应窗函数

switch type
    case 'range'
        % 距离向使用汉明窗
        window = hamming(N);
    case 'azimuth'
        % 方位向使用Kaiser窗
        beta = 8; % Kaiser窗参数
        window = kaiser(N, beta);
    otherwise
        window = ones(N, 1);
end

end

% ==================== 自适应阈值计算 ====================
function threshold = calculate_adaptive_threshold(image)
% 计算自适应能量阈值

image_abs = abs(image);
image_power = image_abs.^2;

% 使用Otsu方法计算阈值
threshold_otsu = graythresh(image_power / max(image_power(:)));

% 使用统计方法计算阈值
mean_power = mean(image_power(:));
std_power = std(image_power(:));
threshold_stat = mean_power + 2 * std_power;

% 组合两种方法
threshold = min(threshold_otsu * max(image_power(:)), threshold_stat);

end

% ==================== 能量阈值应用 ====================
function image_thresholded = apply_energy_threshold(image, threshold)
% 应用能量阈值

image_power = abs(image).^2;
mask = image_power > threshold;

% 软阈值处理
alpha = 0.1; % 软阈值参数
soft_mask = mask + alpha * (1 - mask);

image_thresholded = image .* soft_mask;

end

% ==================== 形态学滤波 ====================
function image_filtered = apply_morphological_filtering(image)
% 应用形态学滤波去除孤立噪声点

image_abs = abs(image);
image_phase = angle(image);

% 创建结构元素
se = strel('disk', 2);

% 形态学开运算去除小的噪声点
image_opened = imopen(image_abs, se);

% 形态学闭运算填充小的空洞
image_closed = imclose(image_opened, se);

% 重构复数图像
image_filtered = image_closed .* exp(1j * image_phase);

end

% ==================== 噪声滤波函数 ====================
function ISAR_image_filtered = apply_noise_filtering(ISAR_image, params_proc)
% 应用多层次噪声滤波

% 方法1：维纳滤波
ISAR_image_wiener = apply_wiener_filtering(ISAR_image);

% 方法2：双边滤波
ISAR_image_bilateral = apply_bilateral_filtering(ISAR_image_wiener);

% 方法3：小波去噪
ISAR_image_wavelet = apply_wavelet_denoising(ISAR_image_bilateral);

ISAR_image_filtered = ISAR_image_wavelet;

end

% ==================== 维纳滤波 ====================
function image_filtered = apply_wiener_filtering(image)
% 应用维纳滤波

% 估计噪声方差
noise_var = estimate_noise_variance(image);

% 应用维纳滤波
image_fft = fft2(image);
[M, N] = size(image);

% 创建频率网格
[u, v] = meshgrid(0:N-1, 0:M-1);
u = u - N/2;
v = v - M/2;

% 估计信号功率谱
signal_power = abs(image_fft).^2;
signal_power_smooth = imgaussfilt(signal_power, 2);

% 维纳滤波器
wiener_filter = signal_power_smooth ./ (signal_power_smooth + noise_var);

% 应用滤波器
image_filtered_fft = fftshift(image_fft) .* wiener_filter;
image_filtered = ifft2(ifftshift(image_filtered_fft));

end

% ==================== 噪声方差估计 ====================
function noise_var = estimate_noise_variance(image)
% 估计图像噪声方差

% 使用拉普拉斯算子估计噪声
laplacian_kernel = [0 -1 0; -1 4 -1; 0 -1 0];
image_abs = abs(image);
laplacian_response = conv2(image_abs, laplacian_kernel, 'same');

% 噪声方差估计
noise_var = var(laplacian_response(:)) / 6;

end

% ==================== 双边滤波 ====================
function image_filtered = apply_bilateral_filtering(image)
% 应用双边滤波保持边缘

image_abs = abs(image);
image_phase = angle(image);

% 双边滤波参数
sigma_spatial = 2;
sigma_intensity = 0.1 * max(image_abs(:));

% 对幅度应用双边滤波
image_abs_filtered = bilateral_filter_2d(image_abs, sigma_spatial, sigma_intensity);

% 重构复数图像
image_filtered = image_abs_filtered .* exp(1j * image_phase);

end

% ==================== 二维双边滤波实现 ====================
function filtered_image = bilateral_filter_2d(image, sigma_spatial, sigma_intensity)
% 二维双边滤波实现

[rows, cols] = size(image);
filtered_image = zeros(size(image));

% 空间高斯核大小
kernel_size = 2 * ceil(3 * sigma_spatial) + 1;
half_size = floor(kernel_size / 2);

for i = 1:rows
    for j = 1:cols
        % 定义邻域
        i_min = max(1, i - half_size);
        i_max = min(rows, i + half_size);
        j_min = max(1, j - half_size);
        j_max = min(cols, j + half_size);
        
        % 提取邻域
        neighborhood = image(i_min:i_max, j_min:j_max);
        
        % 计算空间权重
        [di, dj] = meshgrid(j_min:j_max, i_min:i_max);
        spatial_weights = exp(-((di - j).^2 + (dj - i).^2) / (2 * sigma_spatial^2));
        
        % 计算强度权重
        intensity_diff = abs(neighborhood - image(i, j));
        intensity_weights = exp(-(intensity_diff.^2) / (2 * sigma_intensity^2));
        
        % 组合权重
        combined_weights = spatial_weights .* intensity_weights;
        
        % 归一化并计算滤波值
        filtered_image(i, j) = sum(combined_weights(:) .* neighborhood(:)) / sum(combined_weights(:));
    end
end

end

% ==================== 小波去噪 ====================
function image_denoised = apply_wavelet_denoising(image)
% 应用小波去噪

% 分离幅度和相位
image_abs = abs(image);
image_phase = angle(image);

% 小波分解
wavelet_name = 'db4';
levels = 3;
[C, S] = wavedec2(image_abs, levels, wavelet_name);

% 估计噪声标准差
sigma = median(abs(C(end-S(end,1)*S(end,2)+1:end))) / 0.6745;

% 软阈值去噪
threshold = sigma * sqrt(2 * log(numel(image_abs)));
C_denoised = wthresh(C, 's', threshold);

% 小波重构
image_abs_denoised = waverec2(C_denoised, S, wavelet_name);

% 重构复数图像
image_denoised = image_abs_denoised .* exp(1j * image_phase);

end

% ==================== 对比度增强函数 ====================
function ISAR_image_enhanced = apply_contrast_enhancement(ISAR_image, params_proc)
% 应用对比度增强

image_abs = abs(ISAR_image);
image_phase = angle(ISAR_image);

% 方法1：直方图均衡化
image_eq = histeq(image_abs / max(image_abs(:))) * max(image_abs(:));

% 方法2：自适应直方图均衡化
image_adaptive = adapthisteq(image_abs / max(image_abs(:))) * max(image_abs(:));

% 组合两种方法
alpha = 0.7;
image_enhanced = alpha * image_adaptive + (1 - alpha) * image_eq;

% 重构复数图像
ISAR_image_enhanced = image_enhanced .* exp(1j * image_phase);

end
