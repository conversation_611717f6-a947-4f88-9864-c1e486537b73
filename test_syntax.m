% 主脚本，用于运行ISAR回波仿真和深度融合的VMD-ADMM-DCFT成像处理

clear; close all; clc;

fprintf('开始ISAR成像仿真与深度融合处理...\n');

% -------------------- 1. 数据加载与仿真参数 -------------------- %

fprintf('加载/生成雷达回波数据...\n');

tic;

% 尝试加载实际数据，如果不存在则生成仿真数据

try

load shipx2.mat; % 加载实际数据 shipx2_1000

load s_r_tm2.mat

echo_data = s_r_tm2;

fprintf('实际数据 shipx2_1000.mat 加载成功。\n');

% 为实际数据设置一个基础的sim_params结构体 (部分参数可能需要根据实际数据特性调整)

sim_params = struct();

sim_params.Num_r = size(echo_data, 1);

sim_params.Num_tm = size(echo_data, 2);

sim_params.PRF = 1400; % 假设值, PRF for shipx2_1000 if known, else adjust

sim_params.fc = 5.2e9; % 假设值, Carrier frequency

sim_params.c = 3e8;

sim_params.B = 80e6; % 假设值, Bandwidth

delta_r_res_actual = sim_params.c / (2*sim_params.B);

% 假设目标中心在0米距离，根据距离单元数量和分辨率设定距离轴

% 这部分需要根据实际数据的距离信息进行精确设置

r_center_actual = 0; % 假设目标中心距离

sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);

sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);

catch

fprintf('未找到 shipx2_1000.mat，生成仿真数据...\n');

[echo_data, sim_params] = generate_simulated_echo(); % 保留仿真数据生成作为备选

end

fprintf('数据加载/生成完毕。耗时: %.2f 秒\n', toc);

fprintf('回波数据尺寸: %d (距离单元) x %d (方位单元)\n', size(echo_data, 1), size(echo_data, 2));

% -------------------- 2. 设置处理参数 -------------------- %

params_proc = struct();

% ==================== 增强型VMD参数 ====================
% 针对三维运动舰船目标优化的VMD参数

params_proc.vmd.K = 4; % 增加模态数量以更好分离复杂运动分量

params_proc.vmd.alpha_vmd = 3000; % 增强带宽约束，提高模态分离精度

params_proc.vmd.tau_vmd = 0.1; % 适当的拉格朗日乘子更新率，改善收敛性

params_proc.vmd.tol_vmd_inner = 1e-4; % 更严格的收敛容限

params_proc.vmd.max_iter_vmd_inner = 20; % 增加迭代次数确保充分收敛

params_proc.vmd.init_omega_method = 'adaptive'; % 自适应初始化方法

params_proc.vmd.alpha_phase_guidance = 0.8; % 增强相位引导权重

params_proc.vmd.enable_frequency_tracking = true; % 启用频率跟踪

params_proc.vmd.bandwidth_penalty = 1.5; % 带宽惩罚因子

% ==================== 增强型相位估计参数 ====================
% 针对三维运动的高阶相位补偿

params_proc.phase_est.poly_order = 4; % 增加到4阶以补偿复杂三维运动

params_proc.phase_est.fd_search_range_factor = 0.8; % 扩大搜索范围

params_proc.phase_est.ka_search_pts = 51; % 增加搜索精度

params_proc.phase_est.kb_search_pts = 51; % 增加搜索精度

params_proc.phase_est.kc_search_pts = 31; % 新增三次项搜索点数

params_proc.phase_est.max_iter_phase_inner = 8; % 增加相位估计迭代次数

params_proc.phase_est.enable_global_optimization = true; % 启用全局优化

params_proc.phase_est.motion_model = '3D_coupled'; % 三维耦合运动模型

params_proc.phase_est.adaptive_search = true; % 自适应搜索策略

% ==================== 增强型ADMM参数 ====================
% 深度融合的ADMM优化框架

params_proc.admm_global.rho_X = 2.0; % 增强频谱约束强度

params_proc.admm_global.rho_U = 1.0; % 增强模态分解约束

params_proc.admm_global.rho_P = 1.0; % 增强相位约束

params_proc.admm_global.lambda_sparsity = 0.02; % 优化稀疏正则化权重

params_proc.admm_global.max_iter = 15; % 显著增加ADMM迭代次数

params_proc.admm_global.tol = 1e-4; % 更严格的收敛容限

params_proc.admm_global.alpha_data_fidelity = 1.5; % 增强数据保真权重

params_proc.admm_global.alpha_phase_sharpness = 0.3; % 增强相位聚焦权重

params_proc.admm_global.enable_adaptive_rho = true; % 启用自适应惩罚参数

params_proc.admm_global.convergence_check_interval = 3; % 收敛检查间隔

% ==================== 三维运动补偿参数 ====================
% 专门针对舰船三维运动的补偿参数

params_proc.motion_3d.enable_roll_compensation = true; % 翻滚补偿

params_proc.motion_3d.enable_pitch_compensation = true; % 俯仰补偿

params_proc.motion_3d.enable_yaw_compensation = true; % 偏航补偿

params_proc.motion_3d.coupling_factor = 0.5; % 运动耦合因子

params_proc.motion_3d.motion_estimation_method = 'iterative_ml'; % 最大似然估计

params_proc.motion_3d.reference_scatterer_selection = 'energy_based'; % 参考散射点选择策略

% 其他处理参数

params_proc.num_azimuth = sim_params.Num_tm; % 方位单元数

params_proc.num_range_bins = sim_params.Num_r; % 距离单元数

params_proc.PRF = sim_params.PRF;

params_proc.fc = sim_params.fc;

params_proc.c = sim_params.c;

params_proc.tm_azimuth = sim_params.tm; % 慢时间轴

params_proc.normalized_tm = (0:params_proc.num_azimuth-1) / params_proc.num_azimuth; % 归一化慢时间

% -------------------- 3. 执行增强型三维运动ISAR成像算法 -------------------- %

fprintf('开始执行增强型三维运动ISAR成像算法...\n');

tic;

% 调用增强型算法
[ISAR_image_enhanced, motion_params_estimated, quality_metrics_enhanced] = enhanced_3d_motion_isar(echo_data, params_proc, sim_params);

fprintf('增强型三维运动ISAR成像处理完毕。耗时: %.2f 秒\n', toc);

% 为了兼容性，保留原变量名
ISAR_image_fused = ISAR_image_enhanced;

% 同时运行原算法进行对比
fprintf('运行原算法进行对比...\n');
tic;
[ISAR_image_original, dominant_mode_compensated_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = perform_isar_imaging_fused_admm(echo_data, params_proc, sim_params);
fprintf('原算法处理完毕。耗时: %.2f 秒\n', toc);

% -------------------- 4. 显示结果 -------------------- %

fprintf('显示成像结果...\n');

% --- 原始数据和直接FFT ---

figure('Name', '原始数据和直接FFT');

subplot(1,2,1);

imagesc(sim_params.tm, sim_params.r_axis, abs(echo_data));

xlabel('慢时间 (秒)'); ylabel('距离 (米)'); title('距离压缩后的原始回波'); colorbar; axis xy;

raw_fft = fftshift(fft(echo_data, [], 2), 2);

doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, params_proc.num_azimuth);

subplot(1,2,2);

imagesc(doppler_axis, sim_params.r_axis, abs(raw_fft));

xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('原始数据直接FFT'); colorbar; axis xy;

% --- 增强型算法成像结果 ---

ISAR_image_enhanced_shifted = fftshift(ISAR_image_enhanced, 2);
ISAR_image_original_shifted = fftshift(ISAR_image_original, 2);

figure('Name', '增强型三维运动ISAR成像结果对比');

subplot(2,2,1);
imagesc(doppler_axis, sim_params.r_axis, abs(raw_fft));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('原始数据直接FFT'); colorbar; axis xy;

subplot(2,2,2);
imagesc(doppler_axis, sim_params.r_axis, abs(ISAR_image_original_shifted));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('原VMD-ADMM-DCFT算法'); colorbar; axis xy;

subplot(2,2,3);
imagesc(doppler_axis, sim_params.r_axis, abs(ISAR_image_enhanced_shifted));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('增强型三维运动补偿算法'); colorbar; axis xy;

subplot(2,2,4);
% 显示改善效果
improvement_ratio = abs(ISAR_image_enhanced_shifted) ./ (abs(ISAR_image_original_shifted) + eps);
imagesc(doppler_axis, sim_params.r_axis, improvement_ratio);
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('改善比率 (增强/原始)'); colorbar; axis xy;
caxis([0.5, 2]); % 限制显示范围

% 保持兼容性
ISAR_image_fused_shifted = ISAR_image_enhanced_shifted;

% --- 对数尺度显示 ---

figure('Name', '对数尺度对比');

G_raw = 20*log10(abs(raw_fft)./max(abs(raw_fft(:))));

imagesc(doppler_axis, sim_params.r_axis, G_raw); caxis([-30,0]);

xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('直接FFT (dB)'); colorbar; axis xy; colormap('jet');

figure('Name', '对数尺度对比');

G_fused = 20*log10(abs(ISAR_image_fused_shifted)./max(abs(ISAR_image_fused_shifted(:))));

imagesc(doppler_axis, sim_params.r_axis, G_fused); caxis([-40,0]);

xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('深度融合ADMM (dB)'); colorbar; axis xy; colormap('jet');

% --- (可选) 主导模态补偿 + FFT 结果 (如果算法输出) ---

if exist('dominant_mode_compensated_fft', 'var') && ~isempty(dominant_mode_compensated_fft)

ISAR_image_fft_dominant_shifted = fftshift(dominant_mode_compensated_fft, 2);

figure('Name', '对数尺度对比');

G_dominant = 20*log10(abs(ISAR_image_fft_dominant_shifted)./max(abs(ISAR_image_fft_dominant_shifted(:))));

imagesc(doppler_axis, sim_params.r_axis, G_dominant); caxis([-30,0]);

xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('主导模态补偿+FFT (dB)'); colorbar; axis xy; colormap('jet');

end

% --- 图像质量指标对比 ---

fprintf('\n==================== 算法性能对比 ====================\n');

% 计算各种图像的质量指标
contrast_raw = calculate_image_contrast(abs(raw_fft));
entropy_raw = calculate_image_entropy(abs(raw_fft));

contrast_original = calculate_image_contrast(abs(ISAR_image_original_shifted));
entropy_original = calculate_image_entropy(abs(ISAR_image_original_shifted));

contrast_enhanced = calculate_image_contrast(abs(ISAR_image_enhanced_shifted));
entropy_enhanced = calculate_image_entropy(abs(ISAR_image_enhanced_shifted));

fprintf('直接FFT图像质量指标:\n');
fprintf(' - 对比度: %.4f\n', contrast_raw);
fprintf(' - 熵: %.4f\n', entropy_raw);

fprintf('原VMD-ADMM-DCFT算法质量指标:\n');
fprintf(' - 对比度: %.4f (提升: %.2fx)\n', contrast_original, contrast_original/contrast_raw);
fprintf(' - 熵: %.4f (降低: %.4f)\n', entropy_original, entropy_raw - entropy_original);

fprintf('增强型三维运动补偿算法质量指标:\n');
fprintf(' - 对比度: %.4f (提升: %.2fx)\n', contrast_enhanced, contrast_enhanced/contrast_raw);
fprintf(' - 熵: %.4f (降低: %.4f)\n', entropy_enhanced, entropy_raw - entropy_enhanced);

fprintf('增强型算法相对原算法的改善:\n');
fprintf(' - 对比度改善: %.2fx\n', contrast_enhanced/contrast_original);
fprintf(' - 熵进一步降低: %.4f\n', entropy_original - entropy_enhanced);

% 显示估计的运动参数
fprintf('\n估计的三维运动参数:\n');
fprintf(' - 翻滚角速度: %.4f rad/s\n', motion_params_estimated.roll_rate);
fprintf(' - 俯仰角速度: %.4f rad/s\n', motion_params_estimated.pitch_rate);
fprintf(' - 偏航角速度: %.4f rad/s\n', motion_params_estimated.yaw_rate);

% 为了兼容性，保留原变量名
contrast_fused = contrast_enhanced;
entropy_fused = entropy_enhanced;

% --- 中间过程可视化 ---

% 选择一个有代表性的距离单元进行可视化 (例如，能量最大的或者中间的)

[~, r_idx_vis] = max(sum(abs(echo_data).^2, 2));

if isempty(r_idx_vis), r_idx_vis = round(params_proc.num_range_bins/2); end

% VMD分解结果

if ~isempty(vmd_modes_all_bins) && r_idx_vis <= length(vmd_modes_all_bins) && ~isempty(vmd_modes_all_bins{r_idx_vis})

u_k_vis = vmd_modes_all_bins{r_idx_vis};

figure('Name', sprintf('VMD模态 (距离单元 %d)', r_idx_vis));

for k_plot = 1:size(u_k_vis,1)

subplot(size(u_k_vis,1), 1, k_plot);

plot(params_proc.tm_azimuth, real(u_k_vis(k_plot,:)));

title(sprintf('模态 %d (实部)', k_plot)); xlabel('慢时间 (s)');

end

end

% DCFT相位估计过程 (示例：绘制补偿前后的一个模态的频谱)

if ~isempty(phase_coeffs_all_bins) && r_idx_vis <= length(phase_coeffs_all_bins) && ~isempty(phase_coeffs_all_bins{r_idx_vis})

coeffs_vis = phase_coeffs_all_bins{r_idx_vis}; % K x poly_order

u_k_for_phase_vis = vmd_modes_all_bins{r_idx_vis}; % 使用最终的模态


if ~isempty(u_k_for_phase_vis)

k_mode_to_plot = 1; % 选择第一个模态进行演示

mode_signal = u_k_for_phase_vis(k_mode_to_plot, :);

phase_poly_est = construct_phase_poly(params_proc.normalized_tm, coeffs_vis(k_mode_to_plot,:));

mode_compensated = mode_signal .* exp(-1j * phase_poly_est);

figure('Name', sprintf('DCFT相位补偿效果 (距离单元 %d, 模态 %d)', r_idx_vis, k_mode_to_plot));

subplot(2,1,1);

plot(doppler_axis, abs(fftshift(fft(mode_signal))));

title('补偿前模态频谱'); xlabel('多普勒频率 (Hz)');

subplot(2,1,2);

plot(doppler_axis, abs(fftshift(fft(mode_compensated))));

title('补偿后模态频谱'); xlabel('多普勒频率 (Hz)');

end

end

% ADMM迭代收敛

if ~isempty(admm_convergence_all_bins) && r_idx_vis <= length(admm_convergence_all_bins) && ~isempty(admm_convergence_all_bins{r_idx_vis})

conv_data = admm_convergence_all_bins{r_idx_vis};

figure('Name', sprintf('ADMM收敛曲线 (距离单元 %d)', r_idx_vis));

semilogy(1:length(conv_data.primal_res), conv_data.primal_res, 'b-o', 'DisplayName', 'Primal Residual'); hold on;

semilogy(1:length(conv_data.dual_res), conv_data.dual_res, 'r-x', 'DisplayName', 'Dual Residual'); hold off;

xlabel('ADMM迭代次数'); ylabel('残差'); title('ADMM收敛性'); legend; grid on;

end

% 时频分析对比 (原始信号 vs. 最终补偿叠加信号)

signal_orig_vis = echo_data(r_idx_vis, :);

final_compensated_signal_vis = ifft(ISAR_image_fused(r_idx_vis,:)); % 从最终图像频谱反变换

figure('Name', sprintf('时频分析对比 (距离单元 %d)', r_idx_vis));

subplot(1,2,1);

spectrogram(signal_orig_vis, hamming(64), 32, 256, params_proc.PRF, 'yaxis'); % 参数可调整

title('原始信号时频图');

subplot(1,2,2);

spectrogram(final_compensated_signal_vis, hamming(64), 32, 256, params_proc.PRF, 'yaxis');

title('最终补偿后信号时频图');

fprintf('所有可视化完成。\n');

% perform_isar_imaging_fused_admm.m

% 实现深度融合的VMD-ADMM-DCFT ISAR成像算法

% ADMM作为核心优化框架，迭代更新VMD模态、相位系数和稀疏ISAR图像

function [ISAR_image_sparse, s_compensated_dominant_mode_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = perform_isar_imaging_fused_admm(radar_data, params_proc, sim_params)

% 1. 参数初始化

[num_range_bins, num_azimuth] = size(radar_data);

fprintf(' 处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

% 从params_proc中提取参数

K_vmd = params_proc.vmd.K;

poly_order_phase = params_proc.phase_est.poly_order;

tm_normalized = params_proc.normalized_tm; % VMD和相位估计内部使用的归一化时间

% ADMM全局参数

rho_X = params_proc.admm_global.rho_X; % ADMM rho for X update

rho_U = params_proc.admm_global.rho_U; % ADMM rho for U update (mode decomposition fidelity)

lambda_sparsity = params_proc.admm_global.lambda_sparsity;

max_admm_iter = params_proc.admm_global.max_iter;

admm_tol = params_proc.admm_global.tol;

% 初始化输出

ISAR_image_sparse = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));

s_compensated_dominant_mode_fft = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1)); % 用于对比的非稀疏结果的FFT

% 初始化存储中间结果的cell数组 (用于可视化)

vmd_modes_all_bins = cell(num_range_bins, 1);

phase_coeffs_all_bins = cell(num_range_bins, 1);

admm_convergence_all_bins = cell(num_range_bins, 1);

% 2. 主循环: 处理每个距离单元 (使用parfor并行处理)

fprintf(' 开始逐距离单元处理 (共 %d 个)...\n', num_range_bins);

parfor r_idx = 1:num_range_bins

if mod(r_idx, round(num_range_bins/10)) == 0 && r_idx > 1

fprintf(' 正在处理距离单元: %d/%d\n', r_idx, num_range_bins);

end


signal_orig = radar_data(r_idx, :); % 当前距离单元的信号


% 跳过能量过低的距离单元

if sum(abs(signal_orig).^2) < 1e-12 % 能量阈值

ISAR_image_sparse(r_idx, :) = fft(signal_orig); % 或置零

s_compensated_dominant_mode_fft(r_idx, :) = fft(signal_orig);

continue;

end


% 对当前信号进行归一化，避免数值问题

signal_norm_factor = max(abs(signal_orig));

if signal_norm_factor == 0, signal_norm_factor = 1; end

signal = signal_orig / signal_norm_factor;

% -------- ADMM 迭代变量初始化 --------

% VMD模态 u_k(t)

u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));

% VMD模态中心频率 omega_k (归一化)

omega_k = zeros(K_vmd, 1);

if strcmp(params_proc.vmd.init_omega_method, 'peaks')

[~, locs] = findpeaks(abs(fft(signal)), 'SortStr', 'descend', 'NPeaks', K_vmd);

if ~isempty(locs)

omega_k(1:length(locs)) = (locs-1)/num_azimuth;

end

if length(locs) < K_vmd % 补齐

remaining_indices = (length(locs)+1):K_vmd;

omega_k(remaining_indices) = linspace(0.1, 0.4, length(remaining_indices))';

end

else % linear

for k_idx_init = 1:K_vmd

omega_k(k_idx_init) = (k_idx_init-1)/(K_vmd);

end

end


% 相位多项式系数 p_k = [fd_norm, ka_norm, kb_norm, ...] (针对归一化时间)

poly_coeffs_k = zeros(K_vmd, poly_order_phase);


% 稀疏ISAR图像频谱 X(f)

X_sparse_spectrum = fft(signal); % 初始为直接FFT


% ADMM辅助变量和拉格朗日乘子

% 针对 X = Z (稀疏约束)

Z_aux_X = X_sparse_spectrum; % Z_X in paper

Y_lagrange_X = zeros(size(X_sparse_spectrum), 'like', 1j*signal(1)); % Lambda_X in paper

% 针对 s(t) = sum(u_k(t)) (VMD数据保真)

S_reconstructed_from_modes = sum(u_k, 1);

Y_lagrange_U = zeros(size(signal), 'like', 1j*signal(1)); % Lambda_U in paper


admm_iter_data = struct('primal_res_X', [], 'dual_res_X', [], 'primal_res_U', [], 'dual_res_U', [], 'obj_val', []);

% -------- ADMM 主迭代循环 --------

for iter_admm = 1:max_admm_iter

X_prev = X_sparse_spectrum;

Z_aux_X_prev = Z_aux_X;

u_k_prev_iter = u_k; % 用于U的对偶残差

% 1. 更新 VMD 模态 u_k 和中心频率 omega_k

% 子问题: min_{u_k, omega_k} L_VMD(u_k, omega_k | p_k, X, Y_U)

% L_VMD = F_VMD_modes + (rho_U/2) * ||s - sum(u_k) + Y_U/rho_U||^2

% F_VMD_modes 是VMD的带宽项和相位引导项

% 这里的update_modes_admm需要实现这个最小化

target_signal_for_vmd = signal + Y_lagrange_U / rho_U; % f_hat in VMD update eq.

current_phase_models = zeros(K_vmd, num_azimuth);

for k_idx_phase = 1:K_vmd

current_phase_models(k_idx_phase, :) = construct_phase_poly(tm_normalized, poly_coeffs_k(k_idx_phase,:));

end

[u_k, omega_k] = update_modes_admm(target_signal_for_vmd, u_k, omega_k, current_phase_models, params_proc, rho_U);


S_reconstructed_from_modes_new = sum(u_k, 1);

% 2. 更新相位多项式系数 p_k

% 子问题: min_{p_k} L_Phase(p_k | u_k, X, Y_X)

% L_Phase = F_DCFT_sharpness + (rho_X/2) * ||X - FFT(sum(u_k exp(-jp_k))) + Y_X/rho_X||^2

% 这里的update_phase_coeffs_admm需要实现这个最小化

% X_target_for_phase = X_sparse_spectrum + Y_lagrange_X / rho_X; % Not directly used if phase est. is by sharpness

[poly_coeffs_k, estimated_phases_k] = update_phase_coeffs_admm(u_k, poly_coeffs_k, X_sparse_spectrum, params_proc, tm_normalized, rho_X);


% 3. 构造补偿后的叠加信号 S_comp(f)

s_compensated_time = zeros(1, num_azimuth, 'like', signal(1));

for k_idx = 1:K_vmd

s_compensated_time = s_compensated_time + u_k(k_idx,:) .* exp(-1j * estimated_phases_k(k_idx,:));

end

S_compensated_fft = fft(s_compensated_time);


% 4. 更新稀疏ISAR图像频谱 X(f)

% 子问题: min_X 0.5 * ||X - S_comp(f)||^2 + (rho_X/2) * ||X - Z_X + Y_X/rho_X||^2

X_sparse_spectrum = (S_compensated_fft + rho_X * (Z_aux_X - Y_lagrange_X/rho_X)) / (1 + rho_X);


% 5. 更新辅助变量 Z_X (稀疏投影)

% 子问题: min_Z_X lambda_s * ||Z_X||_1 + (rho_X/2) * ||X - Z_X + Y_X/rho_X||^2

Z_aux_X = soft_threshold(X_sparse_spectrum + Y_lagrange_X/rho_X, lambda_sparsity/rho_X);


% 6. 更新拉格朗日乘子 Y_X 和 Y_U

Y_lagrange_X = Y_lagrange_X + rho_X * (X_sparse_spectrum - Z_aux_X);

Y_lagrange_U = Y_lagrange_U + rho_U * (signal - S_reconstructed_from_modes_new);


% 收敛检查

primal_res_X = norm(X_sparse_spectrum - Z_aux_X) / (norm(X_sparse_spectrum) + eps);

dual_res_X = rho_X * norm(Z_aux_X - Z_aux_X_prev) / (norm(Y_lagrange_X) + eps);


primal_res_U = norm(signal - S_reconstructed_from_modes_new) / (norm(signal) + eps);

dual_res_U = rho_U * norm(S_reconstructed_from_modes_new - sum(u_k_prev_iter,1)) / (norm(Y_lagrange_U) + eps); % Approx.

admm_iter_data.primal_res_X(iter_admm) = primal_res_X;

admm_iter_data.dual_res_X(iter_admm) = dual_res_X;

admm_iter_data.primal_res_U(iter_admm) = primal_res_U;

admm_iter_data.dual_res_U(iter_admm) = dual_res_U;

if iter_admm > 1 && ((primal_res_X < admm_tol && dual_res_X < admm_tol) || (primal_res_U < admm_tol && dual_res_U < admm_tol) ) % Simplified combined check

% fprintf(' ADMM 在距离单元 %d, 第 %d 次迭代收敛。\n', r_idx, iter_admm);

break;

end

end % end ADMM iteration


ISAR_image_sparse(r_idx, :) = X_sparse_spectrum * signal_norm_factor; % 恢复幅度


% 存储中间结果

vmd_modes_all_bins{r_idx} = u_k * signal_norm_factor;

phase_coeffs_all_bins{r_idx} = poly_coeffs_k;

admm_convergence_all_bins{r_idx} = admm_iter_data;

% (可选) 保存用主导模态相位补偿的原始信号，用于对比

% 这部分需要根据最终的u_k和poly_coeffs_k来计算

mode_energies = sum(abs(u_k).^2, 2);

[~, dominant_idx] = max(mode_energies);

dominant_phase_compensation = construct_phase_poly(tm_normalized, poly_coeffs_k(dominant_idx, :));

s_comp_dom_mode_time = signal_orig .* exp(-1j * dominant_phase_compensation);

s_compensated_dominant_mode_fft(r_idx, :) = fft(s_comp_dom_mode_time);

end % end parfor r_idx

fprintf(' 所有距离单元处理完毕。\n');

end

% 辅助函数: 软阈值 (与原代码一致)

function y = soft_threshold(x, threshold_val)

y = sign(x) .* max(abs(x) - threshold_val, 0);

end

% 辅助函数: 构造相位多项式

function phase_poly = construct_phase_poly(tm_normalized, coeffs)

poly_order = length(coeffs);

phase_poly = zeros(size(tm_normalized));

if poly_order >= 1

phase_poly = phase_poly + 2*pi * coeffs(1) * tm_normalized;

end

if poly_order >= 2

phase_poly = phase_poly + 2*pi * 0.5 * coeffs(2) * tm_normalized.^2;

end

if poly_order >= 3

phase_poly = phase_poly + 2*pi * (1/6) * coeffs(3) * tm_normalized.^3;

end

% 可以根据需要扩展到更高阶

end

% update_modes_admm.m

% ADMM子问题：更新VMD模态 u_k 和中心频率 omega_k

% min_{u_k, omega_k} F_VMD_modes + (rho_U/2) * ||target_signal_for_vmd - sum(u_k)||^2

function [u_k_updated, omega_k_updated] = update_modes_admm(target_signal_for_vmd, u_k_prev, omega_k_prev, phase_models_k, params_proc, rho_U)

% 输入:

% target_signal_for_vmd - VMD子问题的目标信号 (原始信号 + Y_U/rho_U)

% u_k_prev - 上一轮迭代的模态 (K x N)

% omega_k_prev - 上一轮迭代的中心频率 (K x 1)

% phase_models_k - 当前相位模型 {phi_k(t)} (K x N), 用于相位引导VMD

% params_proc - 处理参数结构体

% rho_U - ADMM中与数据保真项相关的惩罚参数

% 输出:

% u_k_updated - 更新后的模态 (K x N)

% omega_k_updated - 更新后的中心频率 (K x 1)

% VMD内部参数

alpha_vmd = params_proc.vmd.alpha_vmd; % VMD带宽约束

K = params_proc.vmd.K;

tol_vmd_inner = params_proc.vmd.tol_vmd_inner;

max_iter_vmd_inner = params_proc.vmd.max_iter_vmd_inner;

alpha_phase_guidance = params_proc.vmd.alpha_phase_guidance; % 相位引导权重

N = length(target_signal_for_vmd);

target_signal_fft = fft(target_signal_for_vmd);

f_axis_normalized = params_proc.normalized_tm; % 归一化频率轴 (0 to N-1)/N

% 初始化

u_k = u_k_prev;

omega_k = omega_k_prev;

u_k_fft = zeros(K, N, 'like', 1j*target_signal_fft(1));

for k_idx = 1:K

u_k_fft(k_idx,:) = fft(u_k(k_idx,:));

end

% VMD 子迭代 (在ADMM的每一步中)

% 注意：这里的VMD迭代与原始VMD略有不同，因为目标函数包含rho_U项

% 原始VMD的lambda_hat对应这里的 Y_lagrange_U, tau对应rho_U

% target_signal_for_vmd = signal + Y_lagrange_U / rho_U

% VMD更新u_k时，分子是 target_signal_fft - sum_other_modes_fft

% 分母是 1/(1+tau) * (1 + 2*alpha_vmd*(f - omega_k)^2) (如果tau>0, 即rho_U>0)

% 或者，更直接地，VMD的二次惩罚项 ||f(t) - sum_k u_k(t)||^2_2 被 rho_U/2 * ||...||^2 替代

for iter_inner = 1:max_iter_vmd_inner

u_sum_fft_prev_iter = sum(u_k_fft, 1);


for k_idx = 1:K

% 计算除当前模态外的所有模态之和的fft

sum_other_modes_fft = u_sum_fft_prev_iter - u_k_fft(k_idx,:);


% 在频域更新 u_k

% 分子: target_signal_fft - sum_other_modes_fft

numerator_fft = target_signal_fft - sum_other_modes_fft;


% 分母: (1/rho_U) + 2*alpha_vmd*(f_axis_normalized - omega_k(k_idx)).^2

% (推导自 VMD 的频域更新公式，将二次惩罚的权重从1变为rho_U)

% 或者，如果VMD的二次惩罚项系数是1，而ADMM的rho_U是外部的，则

% 分母是 1 + 2*alpha_vmd*(f - omega_k)^2，但分子需要调整

% 按照标准VMD的ADMM解法，u-subproblem的更新是：

% U_k^{n+1} = (F_hat - sum_{i~=k} U_i^n - sum_{i>k} U_i^{n+1} + Lambda_hat^n/2) / (1 + 2*alpha*(omega-omega_k)^2)

% 在我们的融合ADMM中，Lambda_hat/2 相当于 Y_lagrange_U / (2*rho_U) (如果rho_U是VMD的tau)

% 并且 F_hat 变成了 target_signal_for_vmd_fft

% 这里的 target_signal_for_vmd 已经是 signal + Y_U/rho_U

% 所以，分子是 fft(signal + Y_U/rho_U) - sum_other_modes_fft

% = target_signal_fft - sum_other_modes_fft


denominator = 1 + 2*alpha_vmd*(f_axis_normalized - omega_k(k_idx)).^2;

% 加入相位引导 (如果提供)

if alpha_phase_guidance > 0 && ~isempty(phase_models_k) && size(phase_models_k,1) >= k_idx && any(phase_models_k(k_idx,:))

% 构造相位先验的频谱，假设模态幅度的先验是1 (或从u_k_prev获取)

% phase_prior_signal_time = abs(u_k_prev(k_idx,:)) .* exp(1j * phase_models_k(k_idx,:)); % 幅度引导

phase_prior_signal_time = exp(1j * phase_models_k(k_idx,:)); % 仅相位引导

phase_prior_term_fft = fft(phase_prior_signal_time);


% 加权更新分子和分母 (启发式)

% (F_target - sum U_others + weight*P_fft) / (Denom_vmd + weight)

numerator_fft = numerator_fft + alpha_phase_guidance * phase_prior_term_fft;

denominator = denominator + alpha_phase_guidance;

end


u_k_fft(k_idx,:) = numerator_fft ./ denominator;

u_k(k_idx,:) = ifft(u_k_fft(k_idx,:)); % 更新时域模态


% 更新中心频率 omega_k (重心法)

power_spectrum_uk = abs(u_k_fft(k_idx,:)).^2;

if sum(power_spectrum_uk) > 1e-10

omega_k(k_idx) = sum(f_axis_normalized .* power_spectrum_uk) / sum(power_spectrum_uk);

else

% 若模态能量过低，保持不变或根据初始方法重新初始化

% omega_k(k_idx) = omega_k_prev(k_idx);

end

end % end k_idx loop


% 检查VMD内部收敛性 (基于模态总和的变化)

if iter_inner > 1

u_sum_change = norm(sum(u_k_fft,1) - u_sum_fft_prev_iter_start_loop) / (norm(u_sum_fft_prev_iter_start_loop) + eps);

if u_sum_change < tol_vmd_inner

break;

end

end

u_sum_fft_prev_iter_start_loop = sum(u_k_fft,1);

end % end VMD inner iteration

u_k_updated = u_k;

omega_k_updated = omega_k;

end

% update_phase_coeffs_admm.m

% ADMM子问题：更新相位多项式系数 p_k

% min_{p_k} F_DCFT_sharpness(u_k, p_k) + (rho_X/2) * ||X_target_for_phase - FFT(sum(u_k exp(-jp_k)))||^2

function [poly_coeffs_updated, estimated_phases_updated] = update_phase_coeffs_admm(u_k_curr, poly_coeffs_prev, X_target_spectrum, params_proc, tm_normalized, rho_X)

% 输入:

% u_k_curr - 当前VMD模态 (K x N)

% poly_coeffs_prev - 上一轮迭代的相位系数 (K x poly_order)

% X_target_spectrum - ADMM中相位估计子问题的目标频谱 (X - Y_X/rho_X)

% params_proc - 处理参数结构体

% tm_normalized - 归一化慢时间轴

% rho_X - ADMM中与频谱匹配相关的惩罚参数

% 输出:

% poly_coeffs_updated - 更新后的相位系数 (K x poly_order)

% estimated_phases_updated - 更新后的相位 {phi_k(t)} (K x N)

K = params_proc.vmd.K;

poly_order = params_proc.phase_est.poly_order;

PRF = params_proc.PRF;

alpha_phase_sharpness = params_proc.admm_global.alpha_phase_sharpness; % 权重

poly_coeffs_updated = zeros(K, poly_order);

estimated_phases_updated = zeros(K, length(tm_normalized));

% 逐模态估计相位参数

for k_idx = 1:K

signal_mode = u_k_curr(k_idx,:);

initial_coeffs_k = poly_coeffs_prev(k_idx,:);


if sum(abs(signal_mode)) < 1e-6 % 跳过低能量模态

poly_coeffs_updated(k_idx,:) = 0;

estimated_phases_updated(k_idx,:) = 0;

continue;

end

% 目标函数: J(p) = -Sharpness( FFT(u_k * exp(-j*phi(p))) ) (最大化尖锐度)

% + (rho_X / (2*alpha_phase_sharpness)) * ||X_target - FFT(u_k * exp(-j*phi(p)))||^2 (匹配目标频谱)

% 这里的实现将简化为仅最大化尖锐度，因为直接优化上述耦合项可能复杂

% 采用与原estimate_phase_polynomial类似的搜索方法，但迭代次数受控

% --- 1. 估计 fd (线性项系数, 多普勒中心) ---

signal_mode_fft = fft(signal_mode);

[~, idx_max_fft] = max(abs(signal_mode_fft));

fd_est_norm = (idx_max_fft - 1) / length(signal_mode);

if fd_est_norm > 0.5, fd_est_norm = fd_est_norm - 1; end

current_coeffs = zeros(1, poly_order);

current_coeffs(1) = fd_est_norm;

% --- 2. 估计 ka (二次项系数) ---

if poly_order >= 2

signal_comp_fd = signal_mode .* exp(-1j * 2*pi * current_coeffs(1) * tm_normalized);

max_chirp_rate_hz_s = (PRF/2)^2 * 0.5; % 启发式范围调整

ka_norm_max_abs = max_chirp_rate_hz_s / PRF^2 * 0.5;

ka_search_values = linspace(-ka_norm_max_abs, ka_norm_max_abs, params_proc.phase_est.ka_search_pts);


sharpness_ka = zeros(size(ka_search_values));

for i_ka = 1:length(ka_search_values)

ka_curr = ka_search_values(i_ka);

dechirped_signal = signal_comp_fd .* exp(-1j * 2*pi * 0.5 * ka_curr * tm_normalized.^2);

spectrum_dechirped = abs(fft(dechirped_signal)).^2;

sharpness_ka(i_ka) = sum(spectrum_dechirped.^2); % L4范数作为尖锐度

end

[~, idx_max_ka] = max(sharpness_ka);

current_coeffs(2) = ka_search_values(idx_max_ka);

end

% --- 3. 估计 kb (三次项系数) ---

if poly_order >= 3

phase_comp_fd_ka = 2*pi * (current_coeffs(1) * tm_normalized + 0.5 * current_coeffs(2) * tm_normalized.^2);

signal_comp_fd_ka = signal_mode .* exp(-1j * phase_comp_fd_ka);


kb_norm_max_abs = (PRF/2)^3 / PRF^3 * 0.1; % 启发式范围调整

kb_search_values = linspace(-kb_norm_max_abs, kb_norm_max_abs, params_proc.phase_est.kb_search_pts);

sharpness_kb = zeros(size(kb_search_values));

for i_kb = 1:length(kb_search_values)

kb_curr = kb_search_values(i_kb);

decubic_signal = signal_comp_fd_ka .* exp(-1j * 2*pi * (1/6) * kb_curr * tm_normalized.^3);

spectrum_decubic = abs(fft(decubic_signal)).^2;

sharpness_kb(i_kb) = sum(spectrum_decubic.^2);

end

[~, idx_max_kb] = max(sharpness_kb);

current_coeffs(3) = kb_search_values(idx_max_kb);

end


poly_coeffs_updated(k_idx,:) = current_coeffs;

estimated_phases_updated(k_idx,:) = construct_phase_poly(tm_normalized, current_coeffs);

end

end

% % 辅助函数: 构造相位多项式 (与主函数中一致)

% function phase_poly = construct_phase_poly(tm_normalized, coeffs)

% poly_order = length(coeffs);

% phase_poly = zeros(size(tm_normalized));

% if poly_order >= 1

% phase_poly = phase_poly + 2*pi * coeffs(1) * tm_normalized;

% end

% if poly_order >= 2

% phase_poly = phase_poly + 2*pi * 0.5 * coeffs(2) * tm_normalized.^2;

% end

% if poly_order >= 3

% phase_poly = phase_poly + 2*pi * (1/6) * coeffs(3) * tm_normalized.^3;

% end

% end

% generate_simulated_echo.m

% 生成ISAR仿真回波数据 (与原代码一致)

function [s_r_tm2, sim_params] = generate_simulated_echo()

% 目标散射点模型

Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...

0 -1 0;...

1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...

-9.5 0.2 0.5;...

-9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...

0 1 0;...

1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...

10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... % 尾部

9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... % 头部

5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;... % 机头

5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... % 机头运动

0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... % 中间运动

-5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... % 机尾部

-5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...% 机尾运动

];

x_Pos_orig = Pos(:,1)*5;

y_Pos_orig = Pos(:,2)*5;

z_Pos_orig = Pos(:,3)*5;

x_Pos = x_Pos_orig;

y_Pos = y_Pos_orig;

z_Pos = z_Pos_orig;

R_los = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)];

Num_point = size(x_Pos, 1);

x_r_proj = zeros(1,Num_point);

y_r_proj = zeros(1,Num_point);

z_r_proj = zeros(1,Num_point);

for n_point = 1:Num_point

x_r_proj(n_point) = y_Pos(n_point)*R_los(3) - z_Pos(n_point)*R_los(2);

y_r_proj(n_point) = z_Pos(n_point)*R_los(1) - x_Pos(n_point)*R_los(3);

z_r_proj(n_point) = x_Pos(n_point)*R_los(2) - y_Pos(n_point)*R_los(1);

end

x_omega = 0.05;

y_omega = 0.2;

z_omega = 0.05;

x_alpha_rot = 0.05;

y_alpha_rot = 0.1;

z_alpha_rot = 0.05;

x_beta_rot = 0.05;

y_beta_rot = 0.2;

z_beta_rot = 0.05;

f_v_coeffs = zeros(1,Num_point);

alpha_v_coeffs = zeros(1,Num_point);

beta_v_coeffs = zeros(1,Num_point);

for n_point = 1:Num_point

f_v_coeffs(n_point) = x_r_proj(n_point)*x_omega + y_r_proj(n_point)*y_omega + z_r_proj(n_point)*z_omega;

alpha_v_coeffs(n_point) = x_r_proj(n_point)*x_alpha_rot + y_r_proj(n_point)*y_alpha_rot + z_r_proj(n_point)*z_alpha_rot;

beta_v_coeffs(n_point) = x_r_proj(n_point)*x_beta_rot + y_r_proj(n_point)*y_beta_rot + z_r_proj(n_point)*z_beta_rot;

end

sim_params.B = 80*1e6;

sim_params.c = 3*1e8;

sim_params.PRF = 1400;

sim_params.fc = 5.2*1e9;

delta_r_res = sim_params.c / (2*sim_params.B);

sim_params.r_axis = (-50*delta_r_res : delta_r_res : 50*delta_r_res-delta_r_res); % 确保长度为100

if length(sim_params.r_axis) > 100

sim_params.r_axis = sim_params.r_axis(1:100);

elseif length(sim_params.r_axis) < 100

sim_params.r_axis = linspace(min(sim_params.r_axis), max(sim_params.r_axis), 100);

end

sim_params.tm = (0 : (1/sim_params.PRF) : 0.501);

sim_params.Num_r = length(sim_params.r_axis);

sim_params.Num_tm = length(sim_params.tm);

ones_r_vec = ones(1, sim_params.Num_r);

ones_tm_vec = ones(1, sim_params.Num_tm);

s_r_tm2 = zeros(sim_params.Num_r, sim_params.Num_tm);

Delta_R0_init = zeros(1,Num_point);

fprintf(' 逐散射点生成回波...\n');

for n_point = 1:Num_point

Delta_R0_init(n_point) = x_Pos(n_point)*R_los(1) + y_Pos(n_point)*R_los(2) + z_Pos(n_point)*R_los(3);

Delta_R_t = Delta_R0_init(n_point) + f_v_coeffs(n_point) .* sim_params.tm + (1/2) * alpha_v_coeffs(n_point) .* sim_params.tm.^2 + (1/6) * beta_v_coeffs(n_point) .* sim_params.tm.^3;

phase_term = (4*pi*sim_params.fc/sim_params.c) * Delta_R_t;

amplitude_factor = 1.0;

%if n_point > 53 && n_point < 62

amplitude_factor = 1.3;

%end

s_r_tm2 = s_r_tm2 + amplitude_factor * sinc((2*sim_params.B/sim_params.c) * (sim_params.r_axis.' * ones_tm_vec - ones_r_vec.' * Delta_R_t)) .*exp(1j * ones_r_vec.' * phase_term);

if mod(n_point, 20) == 0

fprintf(' 已处理 %d / %d 个散射点\n', n_point, Num_point);

end

end

fprintf(' 所有散射点回波生成完毕。\n');

end

% calculate_image_contrast.m

% 计算图像对比度 (与原代码一致)

function contrast = calculate_image_contrast(image_abs)

if isempty(image_abs) || numel(image_abs) < 2

contrast = 0;

return;

end

mean_val = mean(image_abs(:));

std_val = std(image_abs(:));

if mean_val == 0

contrast = 0;

else

contrast = std_val / mean_val;

end

end

% calculate_image_entropy.m

% 计算图像熵 (与原代码一致)

function entropy = calculate_image_entropy(image_abs)

if isempty(image_abs)

entropy = NaN;

return;

end

image_power = image_abs(:).^2;

sum_power = sum(image_power);

if sum_power == 0

entropy = 0;

return;

end

normalized_power = image_power / sum_power;

valid_indices = normalized_power > eps;

if ~any(valid_indices)

entropy = 0;

return;

end

entropy = -sum(normalized_power(valid_indices) .* log2(normalized_power(valid_indices)));

end