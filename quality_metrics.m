function quality_metrics = calculate_comprehensive_quality_metrics(ISAR_image_processed, ISAR_image_raw, original_data)
% CALCULATE_COMPREHENSIVE_QUALITY_METRICS - 计算综合质量指标
%
% 该函数计算ISAR成像的多种质量指标，用于评估算法性能
%
% 输入:
%   ISAR_image_processed - 处理后的ISAR图像
%   ISAR_image_raw - 原始ISAR图像
%   original_data - 原始雷达数据
%
% 输出:
%   quality_metrics - 质量指标结构体

quality_metrics = struct();

% ==================== 基本图像质量指标 ====================
fprintf('    计算基本图像质量指标...\n');

% 对比度
quality_metrics.contrast_processed = calculate_image_contrast(abs(ISAR_image_processed));
quality_metrics.contrast_raw = calculate_image_contrast(abs(ISAR_image_raw));
quality_metrics.contrast_improvement = quality_metrics.contrast_processed / quality_metrics.contrast_raw;

% 熵
quality_metrics.entropy_processed = calculate_image_entropy(abs(ISAR_image_processed));
quality_metrics.entropy_raw = calculate_image_entropy(abs(ISAR_image_raw));
quality_metrics.entropy_reduction = quality_metrics.entropy_raw - quality_metrics.entropy_processed;

% 聚焦度
quality_metrics.focus_processed = calculate_focus_measure(ISAR_image_processed);
quality_metrics.focus_raw = calculate_focus_measure(ISAR_image_raw);
quality_metrics.focus_improvement = quality_metrics.focus_processed / quality_metrics.focus_raw;

% ==================== 高级质量指标 ====================
fprintf('    计算高级质量指标...\n');

% 峰值旁瓣比 (PSLR)
quality_metrics.pslr_processed = calculate_pslr(ISAR_image_processed);
quality_metrics.pslr_raw = calculate_pslr(ISAR_image_raw);
quality_metrics.pslr_improvement = quality_metrics.pslr_processed - quality_metrics.pslr_raw;

% 积分旁瓣比 (ISLR)
quality_metrics.islr_processed = calculate_islr(ISAR_image_processed);
quality_metrics.islr_raw = calculate_islr(ISAR_image_raw);
quality_metrics.islr_improvement = quality_metrics.islr_raw - quality_metrics.islr_processed;

% 目标背景比 (TBR)
quality_metrics.tbr_processed = calculate_tbr(ISAR_image_processed);
quality_metrics.tbr_raw = calculate_tbr(ISAR_image_raw);
quality_metrics.tbr_improvement = quality_metrics.tbr_processed / quality_metrics.tbr_raw;

% ==================== 散焦评估指标 ====================
fprintf('    计算散焦评估指标...\n');

% 散焦程度
quality_metrics.defocus_processed = calculate_defocus_measure(ISAR_image_processed);
quality_metrics.defocus_raw = calculate_defocus_measure(ISAR_image_raw);
quality_metrics.defocus_reduction = quality_metrics.defocus_raw - quality_metrics.defocus_processed;

% 运动补偿效果
quality_metrics.motion_compensation_effectiveness = calculate_motion_compensation_effectiveness(ISAR_image_processed, original_data);

% ==================== 综合评分 ====================
fprintf('    计算综合评分...\n');
quality_metrics.overall_score = calculate_overall_quality_score(quality_metrics);

% 打印结果
print_quality_metrics(quality_metrics);

end

% ==================== 对比度计算 ====================
function contrast = calculate_image_contrast(image_abs)
% 计算图像对比度

if isempty(image_abs) || numel(image_abs) < 2
    contrast = 0;
    return;
end

mean_val = mean(image_abs(:));
std_val = std(image_abs(:));

if mean_val == 0
    contrast = 0;
else
    contrast = std_val / mean_val;
end

end

% ==================== 熵计算 ====================
function entropy = calculate_image_entropy(image_abs)
% 计算图像熵

if isempty(image_abs)
    entropy = NaN;
    return;
end

image_power = image_abs(:).^2;
sum_power = sum(image_power);

if sum_power == 0
    entropy = 0;
    return;
end

normalized_power = image_power / sum_power;
valid_indices = normalized_power > eps;

if ~any(valid_indices)
    entropy = 0;
    return;
end

entropy = -sum(normalized_power(valid_indices) .* log2(normalized_power(valid_indices)));

end

% ==================== 聚焦度计算 ====================
function focus_val = calculate_focus_measure(image_data)
% 计算图像聚焦度（基于梯度的方法）

image_abs = abs(image_data);

% 计算梯度
[Gx, Gy] = gradient(image_abs);

% Tenengrad聚焦度量
focus_val = mean(sqrt(Gx(:).^2 + Gy(:).^2));

end

% ==================== 峰值旁瓣比计算 ====================
function pslr_db = calculate_pslr(image_data)
% 计算峰值旁瓣比

image_abs = abs(image_data);
image_power = image_abs.^2;

% 找到主瓣峰值
[max_val, max_idx] = max(image_power(:));
[max_row, max_col] = ind2sub(size(image_power), max_idx);

% 定义主瓣区域（3x3邻域）
main_lobe_size = 3;
row_range = max(1, max_row - floor(main_lobe_size/2)):min(size(image_power,1), max_row + floor(main_lobe_size/2));
col_range = max(1, max_col - floor(main_lobe_size/2)):min(size(image_power,2), max_col + floor(main_lobe_size/2));

% 创建掩码排除主瓣
mask = true(size(image_power));
mask(row_range, col_range) = false;

% 找到最大旁瓣
sidelobe_power = image_power(mask);
max_sidelobe = max(sidelobe_power);

% 计算PSLR
if max_sidelobe > 0
    pslr_db = 10 * log10(max_val / max_sidelobe);
else
    pslr_db = Inf;
end

end

% ==================== 积分旁瓣比计算 ====================
function islr_db = calculate_islr(image_data)
% 计算积分旁瓣比

image_abs = abs(image_data);
image_power = image_abs.^2;

% 找到主瓣峰值
[max_val, max_idx] = max(image_power(:));
[max_row, max_col] = ind2sub(size(image_power), max_idx);

% 定义主瓣区域（5x5邻域）
main_lobe_size = 5;
row_range = max(1, max_row - floor(main_lobe_size/2)):min(size(image_power,1), max_row + floor(main_lobe_size/2));
col_range = max(1, max_col - floor(main_lobe_size/2)):min(size(image_power,2), max_col + floor(main_lobe_size/2));

% 计算主瓣能量
main_lobe_energy = sum(sum(image_power(row_range, col_range)));

% 计算总能量
total_energy = sum(image_power(:));

% 计算旁瓣能量
sidelobe_energy = total_energy - main_lobe_energy;

% 计算ISLR
if sidelobe_energy > 0 && main_lobe_energy > 0
    islr_db = 10 * log10(sidelobe_energy / main_lobe_energy);
else
    islr_db = -Inf;
end

end

% ==================== 目标背景比计算 ====================
function tbr_db = calculate_tbr(image_data)
% 计算目标背景比

image_abs = abs(image_data);
image_power = image_abs.^2;

% 使用Otsu方法分离目标和背景
threshold = graythresh(image_power / max(image_power(:)));
target_mask = image_power > threshold * max(image_power(:));

% 计算目标和背景的平均功率
target_power = mean(image_power(target_mask));
background_power = mean(image_power(~target_mask));

% 计算TBR
if background_power > 0
    tbr_db = 10 * log10(target_power / background_power);
else
    tbr_db = Inf;
end

end

% ==================== 散焦程度计算 ====================
function defocus_measure = calculate_defocus_measure(image_data)
% 计算散焦程度

image_abs = abs(image_data);

% 计算二阶导数（拉普拉斯算子）
laplacian_kernel = [0 -1 0; -1 4 -1; 0 -1 0];
laplacian_response = conv2(image_abs, laplacian_kernel, 'same');

% 散焦度量（拉普拉斯响应的方差）
defocus_measure = var(laplacian_response(:));

end

% ==================== 运动补偿效果计算 ====================
function effectiveness = calculate_motion_compensation_effectiveness(processed_image, original_data)
% 计算运动补偿效果

% 计算原始数据的时频分布方差
original_abs = abs(original_data);
time_variance_original = mean(var(original_abs, [], 2));

% 计算处理后图像的聚焦度
processed_abs = abs(processed_image);
focus_processed = calculate_focus_measure(processed_image);

% 归一化并计算效果
max_focus = max(processed_abs(:));
if max_focus > 0
    normalized_focus = focus_processed / max_focus;
else
    normalized_focus = 0;
end

% 运动补偿效果（聚焦度提升）
effectiveness = normalized_focus / (time_variance_original + eps);

end

% ==================== 综合评分计算 ====================
function overall_score = calculate_overall_quality_score(metrics)
% 计算综合质量评分

% 权重设置
w_contrast = 0.2;
w_focus = 0.25;
w_pslr = 0.2;
w_islr = 0.15;
w_tbr = 0.1;
w_defocus = 0.1;

% 归一化各指标（0-1范围）
contrast_score = min(1, metrics.contrast_improvement / 2);
focus_score = min(1, metrics.focus_improvement / 2);
pslr_score = min(1, max(0, metrics.pslr_improvement / 10));
islr_score = min(1, max(0, -metrics.islr_improvement / 10));
tbr_score = min(1, metrics.tbr_improvement / 2);
defocus_score = min(1, max(0, metrics.defocus_reduction / max(metrics.defocus_raw, eps)));

% 计算加权综合评分
overall_score = w_contrast * contrast_score + ...
                w_focus * focus_score + ...
                w_pslr * pslr_score + ...
                w_islr * islr_score + ...
                w_tbr * tbr_score + ...
                w_defocus * defocus_score;

end

% ==================== 打印质量指标 ====================
function print_quality_metrics(metrics)
% 打印质量指标结果

fprintf('\n==================== 图像质量评估结果 ====================\n');
fprintf('基本质量指标:\n');
fprintf('  对比度: %.4f -> %.4f (提升: %.2fx)\n', metrics.contrast_raw, metrics.contrast_processed, metrics.contrast_improvement);
fprintf('  熵: %.4f -> %.4f (降低: %.4f)\n', metrics.entropy_raw, metrics.entropy_processed, metrics.entropy_reduction);
fprintf('  聚焦度: %.4f -> %.4f (提升: %.2fx)\n', metrics.focus_raw, metrics.focus_processed, metrics.focus_improvement);

fprintf('\n高级质量指标:\n');
fprintf('  峰值旁瓣比: %.2f dB -> %.2f dB (改善: %.2f dB)\n', metrics.pslr_raw, metrics.pslr_processed, metrics.pslr_improvement);
fprintf('  积分旁瓣比: %.2f dB -> %.2f dB (改善: %.2f dB)\n', metrics.islr_raw, metrics.islr_processed, metrics.islr_improvement);
fprintf('  目标背景比: %.2f dB -> %.2f dB (提升: %.2fx)\n', metrics.tbr_raw, metrics.tbr_processed, metrics.tbr_improvement);

fprintf('\n散焦评估:\n');
fprintf('  散焦程度: %.4f -> %.4f (降低: %.4f)\n', metrics.defocus_raw, metrics.defocus_processed, metrics.defocus_reduction);
fprintf('  运动补偿效果: %.4f\n', metrics.motion_compensation_effectiveness);

fprintf('\n综合评分: %.4f (满分1.0)\n', metrics.overall_score);
fprintf('========================================================\n\n');

end
