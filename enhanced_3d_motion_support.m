% ==================== 三维运动相位估计函数 ====================
function [phase_coeffs_3d, estimated_phases] = estimate_3d_motion_phase(u_k, omega_k, params_proc, tm_normalized, global_motion_params)
% 基于三维运动模型的相位估计

K = size(u_k, 1);
poly_order = params_proc.phase_est.poly_order;
phase_coeffs_3d = zeros(K, poly_order);
estimated_phases = zeros(K, length(tm_normalized));

for k_idx = 1:K
    signal_mode = u_k(k_idx, :);
    
    if sum(abs(signal_mode)) < 1e-6
        continue;
    end
    
    % 基于三维运动模型的相位估计
    if strcmp(params_proc.phase_est.motion_model, '3D_coupled')
        phase_coeffs_3d(k_idx, :) = estimate_3d_coupled_phase(signal_mode, tm_normalized, params_proc, global_motion_params);
    else
        % 传统多项式相位估计
        phase_coeffs_3d(k_idx, :) = estimate_polynomial_phase(signal_mode, tm_normalized, params_proc);
    end
    
    % 构造相位
    estimated_phases(k_idx, :) = construct_phase_poly_enhanced(tm_normalized, phase_coeffs_3d(k_idx, :));
end

end

% ==================== 三维耦合相位估计 ====================
function coeffs = estimate_3d_coupled_phase(signal, tm_normalized, params_proc, global_motion_params)
% 基于三维耦合运动模型的相位估计

poly_order = params_proc.phase_est.poly_order;
coeffs = zeros(1, poly_order);

% 基于全局运动参数的初始估计
roll_rate = global_motion_params.roll_rate;
pitch_rate = global_motion_params.pitch_rate;
yaw_rate = global_motion_params.yaw_rate;

% 估计多普勒中心频率（一次项）
signal_fft = fft(signal);
[~, peak_idx] = max(abs(signal_fft));
fd_est_norm = (peak_idx - 1) / length(signal);
if fd_est_norm > 0.5, fd_est_norm = fd_est_norm - 1; end

% 考虑三维运动的耦合效应
coupling_factor = params_proc.motion_3d.coupling_factor;
fd_correction = coupling_factor * (roll_rate + pitch_rate) / (2 * pi);
coeffs(1) = fd_est_norm + fd_correction;

% 估计二次项（加速度项）
if poly_order >= 2
    % 基于偏航角速度估计二次项
    ka_est = yaw_rate^2 / (4 * pi);
    
    % 使用聚焦度优化
    ka_search_range = linspace(-abs(ka_est)*2, abs(ka_est)*2, params_proc.phase_est.ka_search_pts);
    sharpness_ka = zeros(size(ka_search_range));
    
    for i = 1:length(ka_search_range)
        phase_comp = 2*pi * (coeffs(1) * tm_normalized + 0.5 * ka_search_range(i) * tm_normalized.^2);
        compensated_signal = signal .* exp(-1j * phase_comp);
        spectrum = abs(fft(compensated_signal)).^2;
        sharpness_ka(i) = sum(spectrum.^2) / sum(spectrum)^2; % 归一化尖锐度
    end
    
    [~, best_idx] = max(sharpness_ka);
    coeffs(2) = ka_search_range(best_idx);
end

% 估计三次项
if poly_order >= 3
    % 基于运动耦合估计三次项
    kb_est = (roll_rate * pitch_rate) / (6 * pi);
    
    kb_search_range = linspace(-abs(kb_est)*3, abs(kb_est)*3, params_proc.phase_est.kb_search_pts);
    sharpness_kb = zeros(size(kb_search_range));
    
    for i = 1:length(kb_search_range)
        phase_comp = 2*pi * (coeffs(1) * tm_normalized + 0.5 * coeffs(2) * tm_normalized.^2 + ...
                            (1/6) * kb_search_range(i) * tm_normalized.^3);
        compensated_signal = signal .* exp(-1j * phase_comp);
        spectrum = abs(fft(compensated_signal)).^2;
        sharpness_kb(i) = sum(spectrum.^2) / sum(spectrum)^2;
    end
    
    [~, best_idx] = max(sharpness_kb);
    coeffs(3) = kb_search_range(best_idx);
end

% 四次项（如果需要）
if poly_order >= 4
    % 基于高阶运动项估计
    kc_est = (roll_rate * pitch_rate * yaw_rate) / (24 * pi);
    
    kc_search_range = linspace(-abs(kc_est)*2, abs(kc_est)*2, params_proc.phase_est.kc_search_pts);
    sharpness_kc = zeros(size(kc_search_range));
    
    for i = 1:length(kc_search_range)
        phase_comp = 2*pi * (coeffs(1) * tm_normalized + 0.5 * coeffs(2) * tm_normalized.^2 + ...
                            (1/6) * coeffs(3) * tm_normalized.^3 + (1/24) * kc_search_range(i) * tm_normalized.^4);
        compensated_signal = signal .* exp(-1j * phase_comp);
        spectrum = abs(fft(compensated_signal)).^2;
        sharpness_kc(i) = sum(spectrum.^2) / sum(spectrum)^2;
    end
    
    [~, best_idx] = max(sharpness_kc);
    coeffs(4) = kc_search_range(best_idx);
end

end

% ==================== 传统多项式相位估计 ====================
function coeffs = estimate_polynomial_phase(signal, tm_normalized, params_proc)
% 传统的多项式相位估计方法

poly_order = params_proc.phase_est.poly_order;
coeffs = zeros(1, poly_order);

% 估计一次项（多普勒中心）
signal_fft = fft(signal);
[~, peak_idx] = max(abs(signal_fft));
fd_est_norm = (peak_idx - 1) / length(signal);
if fd_est_norm > 0.5, fd_est_norm = fd_est_norm - 1; end
coeffs(1) = fd_est_norm;

% 估计二次项
if poly_order >= 2
    signal_comp_fd = signal .* exp(-1j * 2*pi * coeffs(1) * tm_normalized);
    
    ka_max = 0.1; % 搜索范围
    ka_search = linspace(-ka_max, ka_max, params_proc.phase_est.ka_search_pts);
    sharpness_ka = zeros(size(ka_search));
    
    for i = 1:length(ka_search)
        compensated = signal_comp_fd .* exp(-1j * 2*pi * 0.5 * ka_search(i) * tm_normalized.^2);
        spectrum = abs(fft(compensated)).^2;
        sharpness_ka(i) = sum(spectrum.^2);
    end
    
    [~, best_idx] = max(sharpness_ka);
    coeffs(2) = ka_search(best_idx);
end

% 估计三次项
if poly_order >= 3
    phase_comp_12 = 2*pi * (coeffs(1) * tm_normalized + 0.5 * coeffs(2) * tm_normalized.^2);
    signal_comp_12 = signal .* exp(-1j * phase_comp_12);
    
    kb_max = 0.05;
    kb_search = linspace(-kb_max, kb_max, params_proc.phase_est.kb_search_pts);
    sharpness_kb = zeros(size(kb_search));
    
    for i = 1:length(kb_search)
        compensated = signal_comp_12 .* exp(-1j * 2*pi * (1/6) * kb_search(i) * tm_normalized.^3);
        spectrum = abs(fft(compensated)).^2;
        sharpness_kb(i) = sum(spectrum.^2);
    end
    
    [~, best_idx] = max(sharpness_kb);
    coeffs(3) = kb_search(best_idx);
end

% 四次项
if poly_order >= 4
    phase_comp_123 = 2*pi * (coeffs(1) * tm_normalized + 0.5 * coeffs(2) * tm_normalized.^2 + ...
                             (1/6) * coeffs(3) * tm_normalized.^3);
    signal_comp_123 = signal .* exp(-1j * phase_comp_123);
    
    kc_max = 0.02;
    kc_search = linspace(-kc_max, kc_max, params_proc.phase_est.kc_search_pts);
    sharpness_kc = zeros(size(kc_search));
    
    for i = 1:length(kc_search)
        compensated = signal_comp_123 .* exp(-1j * 2*pi * (1/24) * kc_search(i) * tm_normalized.^4);
        spectrum = abs(fft(compensated)).^2;
        sharpness_kc(i) = sum(spectrum.^2);
    end
    
    [~, best_idx] = max(sharpness_kc);
    coeffs(4) = kc_search(best_idx);
end

end

% ==================== 增强型相位多项式构造 ====================
function phase_poly = construct_phase_poly_enhanced(tm_normalized, coeffs)
% 构造增强型相位多项式

poly_order = length(coeffs);
phase_poly = zeros(size(tm_normalized));

if poly_order >= 1
    phase_poly = phase_poly + 2*pi * coeffs(1) * tm_normalized;
end

if poly_order >= 2
    phase_poly = phase_poly + 2*pi * 0.5 * coeffs(2) * tm_normalized.^2;
end

if poly_order >= 3
    phase_poly = phase_poly + 2*pi * (1/6) * coeffs(3) * tm_normalized.^3;
end

if poly_order >= 4
    phase_poly = phase_poly + 2*pi * (1/24) * coeffs(4) * tm_normalized.^4;
end

end
