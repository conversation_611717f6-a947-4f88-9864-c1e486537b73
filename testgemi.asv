% run_isar_processing_enhanced.m
% 主脚本，用于运行ISAR回波仿真和增强的深度融合VMD-ADMM-DCFT成像处理
clear; close all; clc;
fprintf('开始ISAR成像仿真与增强型深度融合处理...\n');
% -------------------- 1. 数据加载与仿真参数 -------------------- %
fprintf('加载/生成雷达回波数据...\n');
tic;
try
    load shipx2.mat; 
    load s_r_tm2.mat
    echo_data = s_r_tm2;
    fprintf('实际数据 shipx2_1000.mat 加载成功。\n');
    sim_params = struct();
    sim_params.Num_r = size(echo_data, 1);
    sim_params.Num_tm = size(echo_data, 2);
    sim_params.PRF = 1400; 
    sim_params.fc = 5.2e9;  
    sim_params.c = 3e8;
    sim_params.B = 80e6;    
    delta_r_res_actual = sim_params.c / (2*sim_params.B);
    r_center_actual = 0; 
    sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, ...
                                 r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);
    sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);
catch
    fprintf('未找到 shipx2_1000.mat，生成仿真数据...\n');
    [echo_data, sim_params] = generate_simulated_echo(); 
end

% --- 修改：优化预处理，只执行一次去均值 ---
fprintf('对原始回波数据进行预处理 (去均值)...\n');
for r_bin = 1:size(echo_data, 1)
    echo_data(r_bin, :) = echo_data(r_bin, :) - mean(echo_data(r_bin, :));
end
% 设置一个标志，表示已经进行过去均值处理
params_proc.preprocessing_done = true;
% --- 预处理结束 ---

fprintf('数据加载/生成完毕。耗时: %.2f 秒\n', toc);
fprintf('回波数据尺寸: %d (距离单元) x %d (方位单元)\n', size(echo_data, 1), size(echo_data, 2));

% -------------------- 2. 设置处理参数 -------------------- %
params_proc = struct();
params_proc.vmd.K = 3;                  
params_proc.vmd.alpha_vmd = 2000;       
params_proc.vmd.tau_vmd = 0;            
params_proc.vmd.tol_vmd_inner = 1e-5; % 稍提高VMD内部收敛精度
params_proc.vmd.max_iter_vmd_inner = 20; % 稍增加VMD内部迭代
params_proc.vmd.init_omega_method = 'peaks_robust'; % 'peaks' 或 'linear' 或 'peaks_robust'
params_proc.vmd.alpha_phase_guidance = 0.5; 

params_proc.phase_est.poly_order = 3; 
params_proc.phase_est.fd_search_range_factor = 0.5; 
params_proc.phase_est.ka_search_pts = 31;    
params_proc.phase_est.kb_search_pts = 31;    
params_proc.phase_est.sharpness_weight = 0.05; % 调整此权重，可能需要更小以突出匹配项
params_proc.phase_est.num_refinement_passes = 2; % 相位系数迭代优化次数

params_proc.admm_global.rho_X = 1.0;      
params_proc.admm_global.rho_U = 0.5;      
params_proc.admm_global.lambda_sparsity = 0.03; % 尝试减小稀疏权重，观察弱散射点
params_proc.admm_global.max_iter = 2;   % 可适当增加ADMM迭代
params_proc.admm_global.tol = 1e-4;     

params_proc.apply_azimuth_window = true; % 是否在最终FFT前应用窗函数

params_proc.num_azimuth = sim_params.Num_tm; 
params_proc.num_range_bins = sim_params.Num_r; 
params_proc.PRF = sim_params.PRF;
params_proc.fc = sim_params.fc;
params_proc.c = sim_params.c;
params_proc.tm_azimuth = sim_params.tm; 
params_proc.normalized_tm = (0:params_proc.num_azimuth-1) / params_proc.num_azimuth; 

% 明确设置预处理标志
params_proc.preprocessing_done = true;

% -------------------- 3. 执行增强型深度融合ISAR成像算法 -------------------- %
fprintf('开始执行增强型深度融合VMD-ADMM-DCFT ISAR成像算法...\n');
tic;
[ISAR_image_fused, dominant_mode_compensated_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = ...
    perform_isar_imaging_fused_admm_enhanced(echo_data, params_proc, sim_params);
fprintf('增强型深度融合ISAR成像处理完毕。耗时: %.2f 秒\n', toc);

% -------------------- 4. 显示结果 -------------------- %
fprintf('显示成像结果...\n');
figure('Name', '原始数据和直接FFT');
subplot(1,2,1);
imagesc(sim_params.tm, sim_params.r_axis, abs(echo_data));
xlabel('慢时间 (秒)'); ylabel('距离 (米)'); title('距离压缩后的原始回波 (已预处理)'); colorbar; axis xy;

% 对直接FFT也应用预处理（如果原始数据被修改了）
echo_data_for_fft_display = echo_data; % radar_data 是 perform_isar_imaging_fused_admm_enhanced 的输入
for r_bin = 1:size(echo_data_for_fft_display, 1) % 确保显示用的直接FFT也基于去均值数据
    echo_data_for_fft_display(r_bin, :) = echo_data_for_fft_display(r_bin, :) - mean(echo_data_for_fft_display(r_bin, :));
end
raw_fft = fftshift(fft(echo_data_for_fft_display, [], 2), 2);
doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, params_proc.num_azimuth);
subplot(1,2,2);
imagesc(doppler_axis, sim_params.r_axis, abs(raw_fft));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('原始数据直接FFT (预处理后)'); colorbar; axis xy;

figure('Name', '对数尺度对比 - 直接FFT');
G_raw = 20*log10(abs(raw_fft)./max(abs(raw_fft(:)) + eps)); 
imagesc(doppler_axis, sim_params.r_axis, G_raw); caxis([-35,0]); % 调整动态范围
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('直接FFT (dB, 预处理后)'); colorbar; axis xy; colormap('jet');
ISAR_image_fused_shifted=fftshift(ISAR_image_fused,2);
figure('Name', '对数尺度对比 - 增强型融合');
G_fused = 20*log10(abs(ISAR_image_fused_shifted)./max(abs(ISAR_image_fused_shifted(:)) + eps)); 
imagesc(G_fused); caxis([-30,0]); % 调整动态范围
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('增强型融合ADMM (dB)'); colorbar; axis xy; colormap('jet');

if exist('dominant_mode_compensated_fft', 'var') && ~isempty(dominant_mode_compensated_fft)
    ISAR_image_fft_dominant_shifted = fftshift(dominant_mode_compensated_fft, 2);
    figure('Name', '对数尺度对比 - 主导模态补偿');
    G_dominant = 20*log10(abs(ISAR_image_fft_dominant_shifted)./max(abs(ISAR_image_fft_dominant_shifted(:)) + eps)); 
    imagesc(doppler_axis, sim_params.r_axis, G_dominant); caxis([-30,0]);
    xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('主导模态补偿+FFT (dB)'); colorbar; axis xy; colormap('jet');
end
[~, r_idx_vis_candidates] = sort(sum(abs(echo_data).^2, 2), 'descend');
if ~isempty(r_idx_vis_candidates)
    r_idx_vis = r_idx_vis_candidates(1); 
else
    r_idx_vis = round(params_proc.num_range_bins/2); 
end
if isempty(r_idx_vis) || r_idx_vis == 0, r_idx_vis = 1; end 

% -------------------- 辅助函数区 -------------------- %
function [ISAR_image_sparse, s_compensated_dominant_mode_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = ...
    perform_isar_imaging_fused_admm_enhanced(radar_data, params_proc, sim_params)

    [num_range_bins, num_azimuth] = size(radar_data);
    fprintf('  处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

    % 确保preprocessing_done字段存在
    if ~isfield(params_proc, 'preprocessing_done')
        params_proc.preprocessing_done = false;
    end

    K_vmd = params_proc.vmd.K;
    poly_order_phase = params_proc.phase_est.poly_order;
    tm_normalized = params_proc.normalized_tm; 

    rho_X = params_proc.admm_global.rho_X;
    rho_U = params_proc.admm_global.rho_U;
    lambda_sparsity = params_proc.admm_global.lambda_sparsity;
    max_admm_iter = params_proc.admm_global.max_iter;
    admm_tol = params_proc.admm_global.tol;
    
    sharpness_weight = params_proc.phase_est.sharpness_weight; 
    num_phase_refinement_passes = params_proc.phase_est.num_refinement_passes;
    apply_azimuth_window_flag = params_proc.apply_azimuth_window;


    ISAR_image_sparse = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));

    s_compensated_dominant_mode_fft = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
    
    vmd_modes_all_bins = cell(num_range_bins, 1);
    phase_coeffs_all_bins = cell(num_range_bins, 1);
    admm_convergence_all_bins = cell(num_range_bins, 1);

    azimuth_window = ones(1, num_azimuth);
    if apply_azimuth_window_flag
        azimuth_window = hamming(num_azimuth)'; % 使用Hamming窗
    end

    fprintf('  开始逐距离单元处理 (共 %d 个)...\n', num_range_bins);
    % disp()

    for r_idx = 1:num_range_bins
        signal_orig_for_range_bin = radar_data(r_idx, :); % radar_data 已经是预处理过的
        
        if sum(abs(signal_orig_for_range_bin).^2) < 1e-12 * num_azimuth % 调整能量阈值
            ISAR_image_sparse(r_idx, :) = fft(signal_orig_for_range_bin .* azimuth_window); 
            s_compensated_dominant_mode_fft(r_idx, :) = fft(signal_orig_for_range_bin .* azimuth_window);
            admm_iter_data_empty = struct('primal_res_X', [], 'dual_res_X', [], 'primal_res_U', [], 'dual_res_U', []);
            admm_convergence_all_bins{r_idx} = admm_iter_data_empty;
            vmd_modes_all_bins{r_idx} = zeros(K_vmd, num_azimuth, 'like', 1j*signal_orig_for_range_bin(1));
            phase_coeffs_all_bins{r_idx} = zeros(K_vmd, poly_order_phase);
            continue;
        end
        
        signal_norm_factor = max(abs(signal_orig_for_range_bin));
        if signal_norm_factor < eps, signal_norm_factor = 1; end % 避免除以零
        signal = signal_orig_for_range_bin / signal_norm_factor;
        
        % 修改：避免重复去均值，使用预处理标志
        if ~params_proc.preprocessing_done
            signal = signal - mean(signal); % 只有未预处理时才执行
        end

        u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));
        omega_k = zeros(K_vmd, 1);
        
        % Robust VMD initialization
        fft_signal_abs = abs(fft(signal));
        if strcmp(params_proc.vmd.init_omega_method, 'peaks_robust')
            % Simple robustification: ignore peaks too close to DC if K_vmd > 1
            % And ensure some separation. This can be much more sophisticated.
            [pks, locs] = findpeaks(fft_signal_abs, 'SortStr', 'descend');
            valid_omega_idx = 0;
            temp_omega_k = zeros(K_vmd,1);
            min_freq_sep = 0.05; % Minimum separation in normalized frequency
            last_added_omega = -inf;
            for i_pk = 1:length(locs)
                current_omega = (locs(i_pk)-1)/num_azimuth;
                if K_vmd == 1 || abs(current_omega - 0.5) > 0.02 % Avoid DC if not the only mode
                   if abs(current_omega - last_added_omega) > min_freq_sep || valid_omega_idx == 0
                        valid_omega_idx = valid_omega_idx + 1;
                        temp_omega_k(valid_omega_idx) = current_omega;
                        last_added_omega = current_omega;
                        if valid_omega_idx == K_vmd, break; end
                   end
                end
            end
             omega_k(1:valid_omega_idx) = temp_omega_k(1:valid_omega_idx);
        elseif strcmp(params_proc.vmd.init_omega_method, 'peaks')
            [~, locs] = findpeaks(fft_signal_abs, 'SortStr', 'descend', 'NPeaks', K_vmd);
            if ~isempty(locs)
                valid_locs = min(length(locs), K_vmd);
                omega_k(1:valid_locs) = (locs(1:valid_locs)-1)/num_azimuth;
            end
        else % linear
             for k_idx_init = 1:K_vmd, omega_k(k_idx_init) = (k_idx_init-1)/(K_vmd); end
        end
        if any(isnan(omega_k)) || valid_omega_idx < K_vmd % Fallback for remaining omegas
            unset_indices = find(isnan(omega_k) | (1:K_vmd)' > valid_omega_idx);
            if ~isempty(unset_indices)
                 omega_k(unset_indices) = linspace(0.1, 0.4, length(unset_indices))';
            end
        end
        
        poly_coeffs_k = zeros(K_vmd, poly_order_phase); 
        estimated_phases_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1)); 

        % Initialize poly_coeffs_k based on initial omega_k for fd
        if poly_order_phase >= 1
            for k_init_phase = 1:K_vmd
                % omega_k is normalized freq [0,1), convert to [-0.5, 0.5) for fd_norm
                fd_norm_init = omega_k(k_init_phase);
                if fd_norm_init > 0.5, fd_norm_init = fd_norm_init - 1; end
                poly_coeffs_k(k_init_phase, 1) = fd_norm_init;
                estimated_phases_k(k_init_phase, :) = construct_phase_poly(tm_normalized, poly_coeffs_k(k_init_phase,:));
            end
        end

        X_sparse_spectrum = fft(signal .* azimuth_window); % Initial X based on windowed signal
        
        Z_aux_X = X_sparse_spectrum; 
        Y_lagrange_X = zeros(size(X_sparse_spectrum), 'like', 1j*signal(1));
        Y_lagrange_U = zeros(size(signal), 'like', 1j*signal(1));
                                                               
        admm_iter_data = struct('primal_res_X', zeros(1,max_admm_iter), 'dual_res_X', zeros(1,max_admm_iter), ...
                                'primal_res_U', zeros(1,max_admm_iter), 'dual_res_U', zeros(1,max_admm_iter));
        u_k_prev_for_dual_U = u_k; 

        for iter_admm = 1:max_admm_iter
            X_prev_for_dual_X = X_sparse_spectrum; 
            Z_aux_X_prev_for_dual_X = Z_aux_X; 
            
            target_signal_for_vmd = signal + Y_lagrange_U / rho_U; 
            target_signal_for_vmd = target_signal_for_vmd - mean(target_signal_for_vmd); % Ensure VMD input is zero-mean

            current_phase_models_for_vmd = estimated_phases_k; % Use phases from previous ADMM iter
            [u_k, omega_k] = update_modes_admm(target_signal_for_vmd, u_k, omega_k, current_phase_models_for_vmd, params_proc, rho_U);
            
            S_reconstructed_from_modes_new = sum(u_k, 1);
            X_target_global = X_sparse_spectrum + Y_lagrange_X / rho_X;
            
            temp_sum_compensated_others_fft = zeros(1, num_azimuth, 'like', 1j*signal(1));
            for k_other_init = 1:K_vmd % Sum of (u_k_new * exp(-j*phi_k_old))
                temp_sum_compensated_others_fft = temp_sum_compensated_others_fft + ...
                    fft( (u_k(k_other_init,:) - mean(u_k(k_other_init,:))) .* exp(-1j * estimated_phases_k(k_other_init,:)) .* azimuth_window );
            end

            new_poly_coeffs_k = poly_coeffs_k; % Start with previous iteration's coeffs
            new_estimated_phases_k = estimated_phases_k;

            for k_update_phase = 1:K_vmd
                uk_current_mode_processed = u_k(k_update_phase,:) - mean(u_k(k_update_phase,:));
                current_uk_compensated_fft_old_phase = fft(uk_current_mode_processed .* exp(-1j * estimated_phases_k(k_update_phase,:)) .* azimuth_window);
                
                Residual_Target_Spectrum_k = X_target_global - (temp_sum_compensated_others_fft - current_uk_compensated_fft_old_phase);

                [coeffs_k, phase_val_k] = update_phase_coeffs_admm_enhanced(...
                    uk_current_mode_processed, ... % Pass zero-mean u_k
                    poly_coeffs_k(k_update_phase,:), ... 
                    Residual_Target_Spectrum_k, ...
                    params_proc, tm_normalized, sharpness_weight, azimuth_window);
                
                new_poly_coeffs_k(k_update_phase,:) = coeffs_k;
                new_estimated_phases_k(k_update_phase,:) = phase_val_k;
                
                temp_sum_compensated_others_fft = temp_sum_compensated_others_fft - current_uk_compensated_fft_old_phase ...
                                                + fft(uk_current_mode_processed .* exp(-1j*phase_val_k) .* azimuth_window);
            end
            poly_coeffs_k = new_poly_coeffs_k;
            estimated_phases_k = new_estimated_phases_k;
            
            % 修改：构造补偿后的叠加信号时增加零频抑制
            s_compensated_time = zeros(1, num_azimuth, 'like', signal(1));
            for k_idx = 1:K_vmd
                s_compensated_time = s_compensated_time + (u_k(k_idx,:) - mean(u_k(k_idx,:))) .* exp(-1j * estimated_phases_k(k_idx,:));
            end
            
            % 增加零频抑制处理
            s_compensated_fft = fft(s_compensated_time .* azimuth_window);
            
            % 对零频附近应用抑制，减小亮线效应
            dc_suppression_width = 3; % 零频附近的抑制宽度
            dc_idx = 1; % 零频索引
            
            % 创建平滑过渡的抑制权重
            dc_suppression_weight = ones(1, num_azimuth);
            for i = -dc_suppression_width:dc_suppression_width
                idx = mod(dc_idx + i - 1, num_azimuth) + 1;
                suppress_factor = 0.2 + 0.8 * (abs(i)/dc_suppression_width)^2; % 平滑过渡
                dc_suppression_weight(idx) = suppress_factor;
            end
            
            % 应用零频抑制
            s_compensated_fft = s_compensated_fft .* dc_suppression_weight;
            
            X_sparse_spectrum = (s_compensated_fft + rho_X * (Z_aux_X - Y_lagrange_X/rho_X)) / (1 + rho_X);
            
            Z_aux_X = soft_threshold(X_sparse_spectrum + Y_lagrange_X/rho_X, lambda_sparsity/rho_X);
            
            Y_lagrange_X = Y_lagrange_X + rho_X * (X_sparse_spectrum - Z_aux_X);
            Y_lagrange_U = Y_lagrange_U + rho_U * (signal - S_reconstructed_from_modes_new); % signal is already zero-mean from VMD input
            
            norm_X_prev = norm(X_prev_for_dual_X); if norm_X_prev < eps, norm_X_prev = 1; end
            norm_Y_X_curr = norm(Y_lagrange_X); if norm_Y_X_curr < eps, norm_Y_X_curr = 1; end
            norm_signal_curr = norm(signal); if norm_signal_curr < eps, norm_signal_curr = 1; end
            norm_Y_U_curr = norm(Y_lagrange_U); if norm_Y_U_curr < eps, norm_Y_U_curr = 1; end

            primal_res_X_val = norm(X_sparse_spectrum - Z_aux_X) / norm_X_prev;
            dual_res_X_val = rho_X * norm(Z_aux_X - Z_aux_X_prev_for_dual_X) / norm_Y_X_curr;
            primal_res_U_val = norm(signal - S_reconstructed_from_modes_new) / norm_signal_curr;
            dual_res_U_val = rho_U * norm(sum(u_k,1) - sum(u_k_prev_for_dual_U,1)) / norm_Y_U_curr; 
            
            admm_iter_data.primal_res_X(iter_admm) = primal_res_X_val;
            admm_iter_data.dual_res_X(iter_admm) = dual_res_X_val;
            admm_iter_data.primal_res_U(iter_admm) = primal_res_U_val;
            admm_iter_data.dual_res_U(iter_admm) = dual_res_U_val;
            u_k_prev_for_dual_U = u_k; 

            if iter_admm > 1 && ...
               ((primal_res_X_val < admm_tol && dual_res_X_val < admm_tol) && ...
                (primal_res_U_val < admm_tol && dual_res_U_val < admm_tol))
                admm_iter_data.primal_res_X = admm_iter_data.primal_res_X(1:iter_admm);
                admm_iter_data.dual_res_X = admm_iter_data.dual_res_X(1:iter_admm);
                admm_iter_data.primal_res_U = admm_iter_data.primal_res_U(1:iter_admm);
                admm_iter_data.dual_res_U = admm_iter_data.dual_res_U(1:iter_admm);
                break;
            end
        end 
        
        ISAR_image_sparse(r_idx, :) = X_sparse_spectrum * signal_norm_factor;

        disp("Size of X_sparse_spec");
        disp(size(X_sparse_spectrum));
        disp("Size of ");
        disp(size(signal_norm_factor));

        % debug
        if (r_idx == 59)
            ISAR_image_sparse(59, 353)
        end
        vmd_modes_all_bins{r_idx} = u_k * signal_norm_factor;
        phase_coeffs_all_bins{r_idx} = poly_coeffs_k; 
        admm_convergence_all_bins{r_idx} = admm_iter_data;
        
        mode_energies = sum(abs(u_k).^2, 2);
        [~, dominant_idx_candidates] = sort(mode_energies, 'descend');
        if ~isempty(dominant_idx_candidates)
            dominant_idx = dominant_idx_candidates(1);
            dominant_phase_compensation = construct_phase_poly(tm_normalized, poly_coeffs_k(dominant_idx, :));
            
            % 修改：避免重复去均值，直接使用原始信号进行相位补偿
            s_comp_dom_mode_time = signal_orig_for_range_bin .* exp(-1j * dominant_phase_compensation); 
            
            % 只应用窗函数，不重复去均值
            s_compensated_dominant_mode_fft(r_idx, :) = fft(s_comp_dom_mode_time .* azimuth_window);
        else
            % 同样避免重复去均值
            s_compensated_dominant_mode_fft(r_idx, :) = fft(signal_orig_for_range_bin .* azimuth_window);
        end
    end 
    fprintf('  所有距离单元处理完毕。\n');
end

function [poly_coeffs_updated_k, estimated_phase_updated_k] = update_phase_coeffs_admm_enhanced(...
    signal_mode_k, poly_coeffs_prev_k, Residual_Target_Spectrum_k, ...
    params_proc, tm_normalized, sharpness_weight, azimuth_window) % Added azimuth_window

    poly_order = params_proc.phase_est.poly_order;
    PRF = params_proc.PRF; 
    N = length(tm_normalized);
    num_refinement_passes = params_proc.phase_est.num_refinement_passes;

    if sum(abs(signal_mode_k)) < 1e-9 
        poly_coeffs_updated_k = zeros(1, poly_order);
        estimated_phase_updated_k = zeros(1, N);
        return;
    end

    fd_search_range_factor = params_proc.phase_est.fd_search_range_factor;
    num_fd_pts = params_proc.phase_est.ka_search_pts; 
    ka_search_pts_num = params_proc.phase_est.ka_search_pts;
    kb_search_pts_num = params_proc.phase_est.kb_search_pts;
    
    temp_coeffs = poly_coeffs_prev_k; % Start with previous iteration's coefficients
    min_total_cost = inf; % Initialize with a very high cost

    for pass = 1:num_refinement_passes
        % Search fd
        if poly_order >= 1
            fd_center = temp_coeffs(1);
            fd_search_half_range = fd_search_range_factor * 0.5 / (2^ (pass-1) ); % Shrinking search range
            fd_search_values = linspace(fd_center - fd_search_half_range, fd_center + fd_search_half_range, num_fd_pts);
            fd_search_values = utilidad_wrap_phase_coeffs(fd_search_values, 0.5);
            
            current_best_fd_cost = inf;
            best_fd_val_pass = temp_coeffs(1);

            for fd_val = fd_search_values
                temp_coeffs(1) = fd_val;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window); % Apply window
                
                % 修改：调整锐度度量方式，减少对零频的过度聚焦
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps); 
                
                % 修改：对零频附近的匹配误差给予较小的权重，减少亮线效应
                freq_axis_norm = (0:length(compensated_signal_fft)-1)/length(compensated_signal_fft);
                freq_weight = 1.0 - 0.8 * exp(-(freq_axis_norm - 0.5).^2 / 0.01); % 在零频附近权重较小
                weighted_error = abs(Residual_Target_Spectrum_k - compensated_signal_fft).^2 .* freq_weight;
                match_error_val = 0.5 * sum(weighted_error);
                
                % 修改：增加锐度权重，减少匹配误差权重
                cost = -sharpness_weight * 3.0 * sharpness_val + match_error_val * 0.7;

                if cost < current_best_fd_cost
                    current_best_fd_cost = cost;
                    best_fd_val_pass = fd_val;
                end
            end
            temp_coeffs(1) = best_fd_val_pass;
            min_total_cost = current_best_fd_cost; % Update overall min_cost
        end

        % Search ka
        if poly_order >= 2
            ka_center = temp_coeffs(2);
            max_chirp_rate_heuristic = (PRF/2)^2 * 0.1 / (2^ (pass-1) ); % Shrinking search range
            ka_norm_max_abs_heuristic = max_chirp_rate_heuristic / PRF^2 * 0.5;
            ka_search_values = linspace(ka_center - ka_norm_max_abs_heuristic, ka_center + ka_norm_max_abs_heuristic, ka_search_pts_num);
            
            current_best_ka_cost = min_total_cost; % Continue from previous best cost
            best_ka_val_pass = temp_coeffs(2);

            for ka_val = ka_search_values
                temp_coeffs(2) = ka_val;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window);
                
                % 修改：使用与fd搜索相同的权重策略
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps);
                freq_axis_norm = (0:length(compensated_signal_fft)-1)/length(compensated_signal_fft);
                freq_weight = 1.0 - 0.8 * exp(-(freq_axis_norm - 0.5).^2 / 0.01);
                weighted_error = abs(Residual_Target_Spectrum_k - compensated_signal_fft).^2 .* freq_weight;
                match_error_val = 0.5 * sum(weighted_error);
                
                % 使用相同的权重比例
                cost = -sharpness_weight * 3.0 * sharpness_val + match_error_val * 0.7;

                if cost < current_best_ka_cost
                    current_best_ka_cost = cost;
                    best_ka_val_pass = ka_val;
                end
            end
            temp_coeffs(2) = best_ka_val_pass;
            min_total_cost = current_best_ka_cost;
        end

        % Search kb
        if poly_order >= 3
            kb_center = temp_coeffs(3);
            kb_norm_max_abs_heuristic = (PRF/2)^3 / PRF^3 * 0.05 / (2^ (pass-1) ); % Shrinking
            kb_search_values = linspace(kb_center - kb_norm_max_abs_heuristic, kb_center + kb_norm_max_abs_heuristic, kb_search_pts_num);

            current_best_kb_cost = min_total_cost;
            best_kb_val_pass = temp_coeffs(3);

            for kb_val = kb_search_values
                temp_coeffs(3) = kb_val;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window);
                
                % 修改：使用与fd搜索相同的权重策略
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps);
                freq_axis_norm = (0:length(compensated_signal_fft)-1)/length(compensated_signal_fft);
                freq_weight = 1.0 - 0.8 * exp(-(freq_axis_norm - 0.5).^2 / 0.01);
                weighted_error = abs(Residual_Target_Spectrum_k - compensated_signal_fft).^2 .* freq_weight;
                match_error_val = 0.5 * sum(weighted_error);
                
                % 使用相同的权重比例
                cost = -sharpness_weight * 3.0 * sharpness_val + match_error_val * 0.7;

                if cost < current_best_kb_cost
                    current_best_kb_cost = cost;
                    best_kb_val_pass = kb_val;
                end
            end
            temp_coeffs(3) = best_kb_val_pass;
            min_total_cost = current_best_kb_cost;
        end
    end % End refinement passes
    
    poly_coeffs_updated_k = temp_coeffs;
    estimated_phase_updated_k = construct_phase_poly(tm_normalized, poly_coeffs_updated_k);
end

function wrapped_coeffs = utilidad_wrap_phase_coeffs(coeffs, max_abs_val)
    wrapped_coeffs = mod(coeffs + max_abs_val, 2*max_abs_val) - max_abs_val;
end

function [u_k_updated, omega_k_updated] = update_modes_admm(target_signal_for_vmd, u_k_prev, omega_k_prev, phase_models_k, params_proc, rho_U)
    alpha_vmd = params_proc.vmd.alpha_vmd; 
    K = params_proc.vmd.K;
    tol_vmd_inner = params_proc.vmd.tol_vmd_inner;
    max_iter_vmd_inner = params_proc.vmd.max_iter_vmd_inner;
    alpha_phase_guidance = params_proc.vmd.alpha_phase_guidance; 
    
    N = length(target_signal_for_vmd);
    target_signal_fft = fft(target_signal_for_vmd); % target_signal_for_vmd is already zero-mean
    f_axis_normalized = params_proc.normalized_tm; 

    u_k = u_k_prev;
    omega_k = omega_k_prev;
    u_k_fft = zeros(K, N, 'like', 1j*target_signal_fft(1));
    for k_idx = 1:K, u_k_fft(k_idx,:) = fft(u_k(k_idx,:)); end
    
    u_sum_fft_prev_iter_start_loop = sum(u_k_fft,1); 

    for iter_inner = 1:max_iter_vmd_inner
        for k_idx = 1:K
            sum_other_modes_fft = sum(u_k_fft,1) - u_k_fft(k_idx,:); 
            numerator_fft = target_signal_fft - sum_other_modes_fft;
            denominator = 1 + 2*alpha_vmd*(f_axis_normalized - omega_k(k_idx)).^2;
            
            if alpha_phase_guidance > 0 && ~isempty(phase_models_k) && size(phase_models_k,1) >= k_idx && any(phase_models_k(k_idx,:)) && ~all(isnan(phase_models_k(k_idx,:)))
                phase_prior_signal_time = exp(1j * phase_models_k(k_idx,:)); 
                phase_prior_term_fft = fft(phase_prior_signal_time);
                numerator_fft = numerator_fft + alpha_phase_guidance * alpha_vmd * phase_prior_term_fft; 
                denominator = denominator + alpha_phase_guidance * alpha_vmd;
            end
            
            u_k_fft(k_idx,:) = numerator_fft ./ denominator;
            power_spectrum_uk = abs(u_k_fft(k_idx,:)).^2;
            if sum(power_spectrum_uk) > 1e-12
                omega_k(k_idx) = sum(f_axis_normalized .* power_spectrum_uk) / sum(power_spectrum_uk);
                omega_k(k_idx) = mod(omega_k(k_idx), 1); 
            end
        end 
        u_k = ifft(u_k_fft, [], 2); 
        if iter_inner > 1
            current_sum_fft = sum(u_k_fft,1);
            change_sum_fft = norm(current_sum_fft - u_sum_fft_prev_iter_start_loop) / (norm(u_sum_fft_prev_iter_start_loop) + eps);
            if change_sum_fft < tol_vmd_inner, break; end
            u_sum_fft_prev_iter_start_loop = current_sum_fft; 
        else
             u_sum_fft_prev_iter_start_loop = sum(u_k_fft,1); 
        end
    end 
    u_k_updated = u_k; 
    omega_k_updated = omega_k;
end

function [s_r_tm2, sim_params] = generate_simulated_echo()
    Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
           0 -1 0;...
           1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
           -9.5 0.2 0.5;...
           -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
           0 1 0;...
           1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
           10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... 
           9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... 
           5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...   
           5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... 
           0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... 
           -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... 
           -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...
           ];
    x_Pos_orig = Pos(:,1)*0.5; y_Pos_orig = Pos(:,2)*0.5; z_Pos_orig = Pos(:,3)*0.5; % Further reduce scale
    x_Pos = x_Pos_orig; y_Pos = y_Pos_orig; z_Pos = z_Pos_orig;
    R_los = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)]; 
    Num_point = size(x_Pos, 1); 
    x_r_proj = zeros(1,Num_point); y_r_proj = zeros(1,Num_point); z_r_proj = zeros(1,Num_point);
    for n_point = 1:Num_point
        x_r_proj(n_point) = y_Pos(n_point)*R_los(3) - z_Pos(n_point)*R_los(2);
        y_r_proj(n_point) = z_Pos(n_point)*R_los(1) - x_Pos(n_point)*R_los(3);
        z_r_proj(n_point) = x_Pos(n_point)*R_los(2) - y_Pos(n_point)*R_los(1);
    end
    x_omega = 0.25; y_omega = 0.40; z_omega = 0.15; % Slightly increased rotation
    x_alpha_rot = 0.15; y_alpha_rot = 0.25; z_alpha_rot = 0.10;
    x_beta_rot = 0.10; y_beta_rot = 0.15; z_beta_rot = 0.05;
    f_v_coeffs = zeros(1,Num_point); alpha_v_coeffs = zeros(1,Num_point); beta_v_coeffs = zeros(1,Num_point);   
    for n_point = 1:Num_point
        f_v_coeffs(n_point) = x_r_proj(n_point)*x_omega + y_r_proj(n_point)*y_omega + z_r_proj(n_point)*z_omega;
        alpha_v_coeffs(n_point) = x_r_proj(n_point)*x_alpha_rot + y_r_proj(n_point)*y_alpha_rot + z_r_proj(n_point)*z_alpha_rot;
        beta_v_coeffs(n_point) = x_r_proj(n_point)*x_beta_rot + y_r_proj(n_point)*y_beta_rot + z_r_proj(n_point)*z_beta_rot;
    end
    sim_params_default.B = 80*1e6; sim_params_default.c = 3e8; sim_params_default.PRF = 1400; sim_params_default.fc = 5.2e9;   
    sim_params.B = sim_params_default.B; sim_params.c = sim_params_default.c; sim_params.PRF = sim_params_default.PRF; sim_params.fc = sim_params_default.fc;
    delta_r_res = sim_params.c / (2*sim_params.B); 
    num_r_bins_sim = 128; 
    max_dist_sim = (num_r_bins_sim/2) * delta_r_res;
    sim_params.r_axis = linspace(-max_dist_sim, max_dist_sim - delta_r_res, num_r_bins_sim);
    sim_params.Num_r = length(sim_params.r_axis);
    num_tm_sim = 256; 
    sim_params.tm = (0 : (1/sim_params.PRF) : (num_tm_sim-1)/sim_params.PRF ); 
    sim_params.Num_tm = length(sim_params.tm);
    ones_r_vec = ones(1, sim_params.Num_r); ones_tm_vec = ones(1, sim_params.Num_tm);
    s_r_tm2 = zeros(sim_params.Num_r, sim_params.Num_tm, 'like', 1j); 
    Delta_R0_init = zeros(1,Num_point); 
    fprintf('  逐散射点生成回波 (仿真)...\n');
    for n_point = 1:Num_point
        Delta_R0_init(n_point) = x_Pos(n_point)*R_los(1) + y_Pos(n_point)*R_los(2) + z_Pos(n_point)*R_los(3);
        Delta_R_t = Delta_R0_init(n_point) + f_v_coeffs(n_point).*sim_params.tm + (1/2)*alpha_v_coeffs(n_point).*sim_params.tm.^2 + (1/6)*beta_v_coeffs(n_point).*sim_params.tm.^3;
        phase_term = (4*pi*sim_params.fc/sim_params.c) * Delta_R_t;
        amplitude_factor = 1.0 + 0.1*randn(); % Add some amplitude variation
        if n_point > 20 && n_point < 30, amplitude_factor = 1.8 + 0.1*randn(); end
        range_profiles = sinc((2*sim_params.B/sim_params.c) * (sim_params.r_axis.' * ones_tm_vec - ones_r_vec.' * Delta_R_t));
        s_r_tm2 = s_r_tm2 + amplitude_factor * range_profiles .* exp(1j * ones_r_vec.' * phase_term);
    end
    % Add some noise
    snr_db = 20; % Signal-to-noise ratio in dB
    signal_power = mean(abs(s_r_tm2(s_r_tm2~=0)).^2);
    noise_power = signal_power / (10^(snr_db/10));
    noise = sqrt(noise_power/2) * (randn(size(s_r_tm2)) + 1j*randn(size(s_r_tm2)));
    s_r_tm2 = s_r_tm2 + noise;
    fprintf('  所有散射点回波生成完毕 (仿真)。\n');
end

function y = soft_threshold(x, threshold_val)
    y = sign(x) .* max(abs(x) - threshold_val, 0);
end

function phase_poly = construct_phase_poly(tm_normalized, coeffs)
    poly_order = length(coeffs);
    phase_poly = zeros(size(tm_normalized));
    if poly_order >= 1 && ~isnan(coeffs(1)), phase_poly = phase_poly + 2*pi * coeffs(1) * tm_normalized; end
    if poly_order >= 2 && ~isnan(coeffs(2)), phase_poly = phase_poly + 2*pi * 0.5 * coeffs(2) * tm_normalized.^2; end
    if poly_order >= 3 && ~isnan(coeffs(3)), phase_poly = phase_poly + 2*pi * (1/6) * coeffs(3) * tm_normalized.^3; end
end

