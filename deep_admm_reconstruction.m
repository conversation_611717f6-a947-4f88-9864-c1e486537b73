function [X_sparse, admm_metrics] = deep_admm_reconstruction(u_k, estimated_phases, params_proc, original_signal)
% DEEP_ADMM_RECONSTRUCTION - 深度ADMM稀疏重建
%
% 该函数实现了多层次约束的ADMM稀疏重建算法，专门针对三维运动ISAR成像优化
%
% 输入:
%   u_k - VMD分解的模态 (K x N)
%   estimated_phases - 估计的相位 (K x N)
%   params_proc - 处理参数
%   original_signal - 原始信号
%
% 输出:
%   X_sparse - 稀疏重建的频谱
%   admm_metrics - ADMM收敛指标

% 参数提取
rho_X = params_proc.admm_global.rho_X;
rho_U = params_proc.admm_global.rho_U;
lambda_sparsity = params_proc.admm_global.lambda_sparsity;
max_iter = params_proc.admm_global.max_iter;
tol = params_proc.admm_global.tol;
alpha_data_fidelity = params_proc.admm_global.alpha_data_fidelity;

K = size(u_k, 1);
N = size(u_k, 2);

% 构造补偿后的信号
s_compensated = zeros(1, N, 'like', original_signal(1));
for k_idx = 1:K
    s_compensated = s_compensated + u_k(k_idx, :) .* exp(-1j * estimated_phases(k_idx, :));
end

% 初始化ADMM变量
X_sparse = fft(s_compensated); % 稀疏频谱
Z_sparse = X_sparse; % 辅助变量
Y_dual_X = zeros(size(X_sparse), 'like', 1j*X_sparse(1)); % 对偶变量

% 模态约束变量
U_modes = u_k; % 模态变量
Y_dual_U = zeros(size(u_k), 'like', 1j*u_k(1)); % 模态对偶变量

% 自适应参数
if params_proc.admm_global.enable_adaptive_rho
    rho_X_adaptive = rho_X;
    rho_U_adaptive = rho_U;
else
    rho_X_adaptive = rho_X;
    rho_U_adaptive = rho_U;
end

% 收敛指标
admm_metrics = struct();
admm_metrics.primal_residual = zeros(max_iter, 1);
admm_metrics.dual_residual = zeros(max_iter, 1);
admm_metrics.objective_value = zeros(max_iter, 1);

% ADMM主迭代
for iter = 1:max_iter
    X_prev = X_sparse;
    Z_prev = Z_sparse;
    U_prev = U_modes;
    
    % ==================== 更新X（频谱变量）====================
    % 子问题: min_X ||X - S_comp_fft||^2 + rho_X/2 * ||X - Z + Y_X/rho_X||^2
    S_comp_fft = fft(s_compensated);
    X_sparse = (alpha_data_fidelity * S_comp_fft + rho_X_adaptive * (Z_sparse - Y_dual_X/rho_X_adaptive)) / ...
               (alpha_data_fidelity + rho_X_adaptive);
    
    % ==================== 更新Z（稀疏辅助变量）====================
    % 子问题: min_Z lambda_s * ||Z||_1 + rho_X/2 * ||X - Z + Y_X/rho_X||^2
    Z_input = X_sparse + Y_dual_X/rho_X_adaptive;
    Z_sparse = enhanced_soft_threshold(Z_input, lambda_sparsity/rho_X_adaptive, params_proc);
    
    % ==================== 更新U（模态变量）====================
    % 子问题: min_U ||sum(U_k) - s||^2 + rho_U/2 * ||U - u_k + Y_U/rho_U||^2
    target_signal_for_modes = original_signal + Y_dual_U / rho_U_adaptive;
    U_modes = update_modes_with_constraint(U_modes, target_signal_for_modes, rho_U_adaptive, params_proc);
    
    % ==================== 更新对偶变量 ====================
    Y_dual_X = Y_dual_X + rho_X_adaptive * (X_sparse - Z_sparse);
    Y_dual_U = Y_dual_U + rho_U_adaptive * (U_modes - u_k);
    
    % ==================== 自适应参数调整 ====================
    if params_proc.admm_global.enable_adaptive_rho && mod(iter, params_proc.admm_global.convergence_check_interval) == 0
        [rho_X_adaptive, rho_U_adaptive] = adaptive_rho_update(X_sparse, Z_sparse, U_modes, u_k, ...
                                                               X_prev, Z_prev, U_prev, ...
                                                               rho_X_adaptive, rho_U_adaptive);
    end
    
    % ==================== 收敛检查 ====================
    % 原始残差
    primal_res_X = norm(X_sparse - Z_sparse) / (norm(X_sparse) + eps);
    primal_res_U = norm(sum(U_modes, 1) - original_signal) / (norm(original_signal) + eps);
    
    % 对偶残差
    dual_res_X = rho_X_adaptive * norm(Z_sparse - Z_prev) / (norm(Y_dual_X) + eps);
    dual_res_U = rho_U_adaptive * norm(U_modes - U_prev, 'fro') / (norm(Y_dual_U, 'fro') + eps);
    
    % 目标函数值
    data_fidelity_term = norm(X_sparse - S_comp_fft)^2;
    sparsity_term = sum(abs(Z_sparse(:)));
    mode_fidelity_term = norm(sum(U_modes, 1) - original_signal)^2;
    
    objective_val = alpha_data_fidelity * data_fidelity_term + lambda_sparsity * sparsity_term + mode_fidelity_term;
    
    % 存储指标
    admm_metrics.primal_residual(iter) = max(primal_res_X, primal_res_U);
    admm_metrics.dual_residual(iter) = max(dual_res_X, dual_res_U);
    admm_metrics.objective_value(iter) = objective_val;
    
    % 检查收敛
    if iter > 1 && admm_metrics.primal_residual(iter) < tol && admm_metrics.dual_residual(iter) < tol
        fprintf('    ADMM在第%d次迭代收敛\n', iter);
        break;
    end
    
    % 更新补偿信号
    s_compensated = zeros(1, N, 'like', original_signal(1));
    for k_idx = 1:K
        s_compensated = s_compensated + U_modes(k_idx, :) .* exp(-1j * estimated_phases(k_idx, :));
    end
end

% 截断未使用的指标
admm_metrics.primal_residual = admm_metrics.primal_residual(1:iter);
admm_metrics.dual_residual = admm_metrics.dual_residual(1:iter);
admm_metrics.objective_value = admm_metrics.objective_value(1:iter);

end

% ==================== 增强型软阈值函数 ====================
function Z = enhanced_soft_threshold(X, threshold, params_proc)
% 增强型软阈值，考虑频率连续性和能量分布

% 基本软阈值
Z_basic = sign(X) .* max(abs(X) - threshold, 0);

% 频率连续性约束
if isfield(params_proc.admm_global, 'enable_frequency_continuity') && params_proc.admm_global.enable_frequency_continuity
    % 应用低通滤波保持频率连续性
    Z = apply_frequency_continuity_constraint(Z_basic);
else
    Z = Z_basic;
end

% 能量保持约束
energy_original = sum(abs(X).^2);
energy_thresholded = sum(abs(Z).^2);

if energy_thresholded > 0
    energy_ratio = sqrt(energy_original / energy_thresholded);
    if energy_ratio > 1.5 % 避免过度放大
        energy_ratio = 1.5;
    end
    Z = Z * min(energy_ratio, 1.2); % 适度补偿能量损失
end

end

% ==================== 频率连续性约束 ====================
function Z_smooth = apply_frequency_continuity_constraint(Z)
% 应用频率连续性约束

% 设计低通滤波器
N = length(Z);
cutoff_freq = 0.1; % 截止频率
filter_order = 4;

% 创建Butterworth低通滤波器
[b, a] = butter(filter_order, cutoff_freq);

% 分别对实部和虚部进行滤波
Z_real_filtered = filtfilt(b, a, real(Z));
Z_imag_filtered = filtfilt(b, a, imag(Z));

% 组合滤波结果
Z_filtered = Z_real_filtered + 1j * Z_imag_filtered;

% 加权组合原始和滤波结果
alpha_smooth = 0.3; % 平滑权重
Z_smooth = (1 - alpha_smooth) * Z + alpha_smooth * Z_filtered;

end

% ==================== 模态约束更新 ====================
function U_updated = update_modes_with_constraint(U_current, target_signal, rho_U, params_proc)
% 带约束的模态更新

K = size(U_current, 1);
N = size(U_current, 2);
U_updated = U_current;

% 数据保真项权重
alpha_mode_fidelity = 1.0;

% 逐模态更新
for k_idx = 1:K
    % 其他模态的和
    sum_other_modes = sum(U_current, 1) - U_current(k_idx, :);
    
    % 目标信号减去其他模态
    target_for_current_mode = target_signal - sum_other_modes;
    
    % 更新当前模态
    U_updated(k_idx, :) = (alpha_mode_fidelity * target_for_current_mode + rho_U * U_current(k_idx, :)) / ...
                          (alpha_mode_fidelity + rho_U);
end

% 归一化约束（可选）
if isfield(params_proc.admm_global, 'enable_mode_normalization') && params_proc.admm_global.enable_mode_normalization
    for k_idx = 1:K
        mode_energy = norm(U_updated(k_idx, :));
        if mode_energy > 0
            U_updated(k_idx, :) = U_updated(k_idx, :) / mode_energy * norm(U_current(k_idx, :));
        end
    end
end

end

% ==================== 自适应参数更新 ====================
function [rho_X_new, rho_U_new] = adaptive_rho_update(X, Z, U, u_k, X_prev, Z_prev, U_prev, rho_X, rho_U)
% 自适应更新ADMM惩罚参数

% 计算残差比率
primal_res_X = norm(X - Z);
dual_res_X = norm(Z - Z_prev);

primal_res_U = norm(sum(U, 1) - sum(u_k, 1));
dual_res_U = norm(U - U_prev, 'fro');

% 更新规则
tau_incr = 2.0;
tau_decr = 2.0;
mu = 10;

% 更新rho_X
if primal_res_X > mu * dual_res_X
    rho_X_new = rho_X * tau_incr;
elseif dual_res_X > mu * primal_res_X
    rho_X_new = rho_X / tau_decr;
else
    rho_X_new = rho_X;
end

% 更新rho_U
if primal_res_U > mu * dual_res_U
    rho_U_new = rho_U * tau_incr;
elseif dual_res_U > mu * primal_res_U
    rho_U_new = rho_U / tau_decr;
else
    rho_U_new = rho_U;
end

% 限制参数范围
rho_X_new = max(0.1, min(100, rho_X_new));
rho_U_new = max(0.1, min(100, rho_U_new));

end
