function [ISAR_image_enhanced, motion_params, quality_metrics] = enhanced_3d_motion_isar(radar_data, params_proc, sim_params)
% ENHANCED_3D_MOTION_ISAR - 增强型三维运动舰船ISAR成像算法
%
% 该算法专门针对具有翻滚、俯仰、偏航复杂三维运动的舰船目标，
% 采用深度融合的VMD-ADMM-DCFT框架，实现高质量ISAR成像。
%
% 核心创新：
% 1. 三维运动耦合建模 - 考虑翻滚、俯仰、偏航的相互影响
% 2. 自适应VMD分解 - 动态调整模态数量和参数
% 3. 全局相位优化 - 基于最大似然估计的相位误差补偿
% 4. 深度ADMM融合 - 多层次约束的稀疏重建
%
% 输入:
%   radar_data - 距离压缩后的回波数据 (距离单元 x 方位单元)
%   params_proc - 处理参数结构体
%   sim_params - 仿真参数结构体
%
% 输出:
%   ISAR_image_enhanced - 增强型ISAR图像
%   motion_params - 估计的三维运动参数
%   quality_metrics - 成像质量指标

fprintf('开始增强型三维运动ISAR成像处理...\n');

[num_range_bins, num_azimuth] = size(radar_data);
tm_normalized = (0:num_azimuth-1) / num_azimuth;

% 初始化输出
ISAR_image_enhanced = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
motion_params = struct();
quality_metrics = struct();

% ==================== 第一阶段：全局运动参数估计 ====================
fprintf('第一阶段：全局运动参数估计...\n');
[global_motion_params, reference_scatterers] = estimate_global_3d_motion(radar_data, params_proc, sim_params);

% ==================== 第二阶段：自适应VMD分解 ====================
fprintf('第二阶段：自适应VMD分解...\n');

% 并行处理每个距离单元
for r_idx = 1:num_range_bins
    if mod(r_idx, round(num_range_bins/10)) == 0
        fprintf('  处理距离单元: %d/%d\n', r_idx, num_range_bins);
    end

    signal_orig = radar_data(r_idx, :);

    % 跳过低能量距离单元
    if sum(abs(signal_orig).^2) < 1e-12
        ISAR_image_enhanced(r_idx, :) = fft(signal_orig);
        continue;
    end

    % 信号归一化
    signal_norm_factor = max(abs(signal_orig));
    if signal_norm_factor == 0, signal_norm_factor = 1; end
    signal = signal_orig / signal_norm_factor;

    % ==================== 自适应VMD分解 ====================
    [u_k, omega_k, vmd_quality] = adaptive_vmd_decompose_3d(signal, params_proc, global_motion_params);

    % ==================== 三维运动相位估计 ====================
    [phase_coeffs_3d, estimated_phases] = estimate_3d_motion_phase(u_k, omega_k, params_proc, tm_normalized, global_motion_params);

    % ==================== 深度ADMM稀疏重建 ====================
    [X_sparse, admm_metrics] = deep_admm_reconstruction(u_k, estimated_phases, params_proc, signal);

    % 存储结果
    ISAR_image_enhanced(r_idx, :) = X_sparse * signal_norm_factor;
end

% ==================== 第三阶段：后处理和质量评估 ====================
fprintf('第三阶段：后处理和质量评估...\n');
[ISAR_image_enhanced, quality_metrics] = post_process_and_evaluate(ISAR_image_enhanced, radar_data, params_proc);

motion_params = global_motion_params;
fprintf('增强型三维运动ISAR成像处理完成。\n');

end

% ==================== 子函数：全局三维运动参数估计 ====================
function [global_motion_params, reference_scatterers] = estimate_global_3d_motion(radar_data, params_proc, sim_params)
% 基于最大似然估计的全局三维运动参数估计

[num_range_bins, num_azimuth] = size(radar_data);
tm = (0:num_azimuth-1) / params_proc.PRF;

% 选择参考散射点（基于能量和稳定性）
energy_profile = sum(abs(radar_data).^2, 2);
[~, sorted_indices] = sort(energy_profile, 'descend');

% 选择前20%的高能量距离单元作为候选
num_candidates = max(5, round(num_range_bins * 0.2));
candidate_indices = sorted_indices(1:num_candidates);

% 从候选中选择最稳定的散射点
stability_scores = zeros(size(candidate_indices));
for i = 1:length(candidate_indices)
    r_idx = candidate_indices(i);
    signal = radar_data(r_idx, :);

    % 计算信号的时频稳定性
    [~, f, t, P] = spectrogram(signal, hamming(32), 16, 64, params_proc.PRF);
    stability_scores(i) = calculate_tf_stability(P);
end

[~, best_indices] = sort(stability_scores, 'descend');
reference_scatterers = candidate_indices(best_indices(1:min(3, length(best_indices))));

% 基于参考散射点估计全局运动参数
global_motion_params = struct();
global_motion_params.roll_rate = 0;
global_motion_params.pitch_rate = 0;
global_motion_params.yaw_rate = 0;
global_motion_params.roll_accel = 0;
global_motion_params.pitch_accel = 0;
global_motion_params.yaw_accel = 0;

% 使用最大似然估计方法
for ref_idx = reference_scatterers'
    signal_ref = radar_data(ref_idx, :);

    % 估计该散射点的运动参数
    motion_params_local = ml_estimate_motion_params(signal_ref, tm, params_proc);

    % 累积到全局参数
    global_motion_params.roll_rate = global_motion_params.roll_rate + motion_params_local.roll_rate;
    global_motion_params.pitch_rate = global_motion_params.pitch_rate + motion_params_local.pitch_rate;
    global_motion_params.yaw_rate = global_motion_params.yaw_rate + motion_params_local.yaw_rate;
end

% 平均化
num_refs = length(reference_scatterers);
global_motion_params.roll_rate = global_motion_params.roll_rate / num_refs;
global_motion_params.pitch_rate = global_motion_params.pitch_rate / num_refs;
global_motion_params.yaw_rate = global_motion_params.yaw_rate / num_refs;

fprintf('  估计的全局运动参数：\n');
fprintf('    翻滚角速度: %.4f rad/s\n', global_motion_params.roll_rate);
fprintf('    俯仰角速度: %.4f rad/s\n', global_motion_params.pitch_rate);
fprintf('    偏航角速度: %.4f rad/s\n', global_motion_params.yaw_rate);

end

% ==================== 子函数：时频稳定性计算 ====================
function stability = calculate_tf_stability(P)
% 计算时频图的稳定性指标

% 归一化功率谱
P_norm = P / max(P(:));

% 计算时间维度的方差（频率稳定性）
freq_variance = var(P_norm, [], 2);
freq_stability = 1 / (1 + mean(freq_variance));

% 计算频率维度的方差（时间稳定性）
time_variance = var(P_norm, [], 1);
time_stability = 1 / (1 + mean(time_variance));

% 综合稳定性
stability = sqrt(freq_stability * time_stability);

end

% ==================== 子函数：最大似然运动参数估计 ====================
function motion_params = ml_estimate_motion_params(signal, tm, params_proc)
% 基于最大似然估计的运动参数估计

motion_params = struct();

% 简化的运动模型：假设主要是偏航运动
signal_fft = fft(signal);
[~, peak_idx] = max(abs(signal_fft));

% 估计多普勒中心频率
if peak_idx > length(signal)/2
    doppler_center = (peak_idx - length(signal) - 1) / length(signal) * params_proc.PRF;
else
    doppler_center = (peak_idx - 1) / length(signal) * params_proc.PRF;
end

% 基于多普勒频率估计角速度
% 简化假设：主要贡献来自偏航运动
lambda = 3e8 / params_proc.fc;
estimated_velocity = doppler_center * lambda / 2;

% 假设目标距离为1000米（可根据实际情况调整）
target_range = 1000;
motion_params.yaw_rate = estimated_velocity / target_range;
motion_params.roll_rate = motion_params.yaw_rate * 0.1; % 假设翻滚为偏航的10%
motion_params.pitch_rate = motion_params.yaw_rate * 0.05; % 假设俯仰为偏航的5%

end

% ==================== 子函数：自适应VMD分解 ====================
function [u_k, omega_k, vmd_quality] = adaptive_vmd_decompose_3d(signal, params_proc, global_motion_params)
% 针对三维运动的自适应VMD分解

K = params_proc.vmd.K;
alpha = params_proc.vmd.alpha_vmd;
tau = params_proc.vmd.tau_vmd;
tol = params_proc.vmd.tol_vmd_inner;
max_iter = params_proc.vmd.max_iter_vmd_inner;

N = length(signal);
u_k = zeros(K, N, 'like', 1j);
omega_k = zeros(K, 1);

% 自适应初始化中心频率
if strcmp(params_proc.vmd.init_omega_method, 'adaptive')
    omega_k = adaptive_omega_initialization(signal, K, global_motion_params);
else
    % 基于峰值的初始化
    signal_fft = fft(signal);
    [~, locs] = findpeaks(abs(signal_fft), 'SortStr', 'descend', 'NPeaks', K);
    if ~isempty(locs)
        omega_k(1:length(locs)) = (locs-1)/N;
    end
    if length(locs) < K
        remaining = (length(locs)+1):K;
        omega_k(remaining) = linspace(0.1, 0.4, length(remaining))';
    end
end

% VMD迭代
signal_fft = fft(signal);
u_k_fft = zeros(K, N, 'like', 1j);
lambda_dual = zeros(1, N, 'like', 1j);

for iter = 1:max_iter
    u_k_fft_prev = u_k_fft;

    for k_idx = 1:K
        % 计算其他模态的和
        sum_other_modes = sum(u_k_fft, 1) - u_k_fft(k_idx, :);

        % 频率轴
        f_axis = (0:N-1)/N;

        % 更新模态
        numerator = signal_fft - sum_other_modes + lambda_dual/2;
        denominator = 1 + 2*alpha*(f_axis - omega_k(k_idx)).^2;

        u_k_fft(k_idx, :) = numerator ./ denominator;
        u_k(k_idx, :) = ifft(u_k_fft(k_idx, :));

        % 更新中心频率
        power_spectrum = abs(u_k_fft(k_idx, :)).^2;
        if sum(power_spectrum) > 1e-12
            omega_k(k_idx) = sum(f_axis .* power_spectrum) / sum(power_spectrum);
        end
    end

    % 更新拉格朗日乘子
    if tau > 0
        lambda_dual = lambda_dual + tau * (signal_fft - sum(u_k_fft, 1));
    end

    % 收敛检查
    if iter > 1
        change = norm(sum(u_k_fft, 1) - sum(u_k_fft_prev, 1)) / (norm(sum(u_k_fft_prev, 1)) + eps);
        if change < tol
            break;
        end
    end
end

% 计算分解质量
vmd_quality = calculate_vmd_quality(u_k, signal);

end

% ==================== 子函数：自适应频率初始化 ====================
function omega_k = adaptive_omega_initialization(signal, K, global_motion_params)
% 基于全局运动参数的自适应频率初始化

N = length(signal);
signal_fft = fft(signal);

% 基于运动参数预测可能的频率分布
expected_doppler_spread = abs(global_motion_params.yaw_rate) * 100; % 简化估计
expected_center_freq = 0.1; % 基于经验

% 在预期频率范围内均匀分布
freq_range = min(0.4, expected_doppler_spread);
omega_k = linspace(expected_center_freq - freq_range/2, ...
                   expected_center_freq + freq_range/2, K)';

% 确保频率在有效范围内
omega_k = max(0.01, min(0.49, omega_k));

end

% ==================== 子函数：VMD质量评估 ====================
function quality = calculate_vmd_quality(u_k, original_signal)
% 计算VMD分解质量

% 重构误差
reconstructed = sum(u_k, 1);
reconstruction_error = norm(original_signal - reconstructed) / norm(original_signal);

% 模态正交性
K = size(u_k, 1);
orthogonality = 0;
for i = 1:K
    for j = i+1:K
        correlation = abs(sum(conj(u_k(i,:)) .* u_k(j,:))) / ...
                     (norm(u_k(i,:)) * norm(u_k(j,:)) + eps);
        orthogonality = orthogonality + correlation;
    end
end
orthogonality = orthogonality / (K*(K-1)/2);

% 综合质量指标
quality = (1 - reconstruction_error) * (1 - orthogonality);

end